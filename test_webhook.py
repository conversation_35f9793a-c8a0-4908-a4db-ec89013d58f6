import requests
import json

def test_webhook(webhook_url):
    # Test data matching the expected format
    data = {
        "content": "Crimson has received a payment ╽ From: TestUser1aa232 ╽ Price: $25.aa002 ╽ Package: VIP Pacaka2age ╽ Transaction ID: TEST2a123 ╽ Email: <EMAIL>"
    }

    try:
        # Send test webhook
        response = requests.post(webhook_url, json=data)
        
        # Discord webhook successful response is 204
        if response.status_code == 204:
            print("✓ Webhook test successful - Message sent to <PERSON>rd")
        else:
            print(f"⚠ Webhook Response Status: {response.status_code}")
            if response.text:
                print(f"Response Details: {response.text}")
            else:
                print("No additional response details available")
                
    except requests.exceptions.RequestException as e:
        print(f"⚠ Connection Error: {str(e)}")
    except Exception as e:
        print(f"⚠ Unexpected Error: {str(e)}")

if __name__ == "__main__":
    # Replace with your webhook URL
    webhook_url = "https://discord.com/api/webhooks/1378086188262490142/dpQesZefRzldGSouDg0ieytvKq08eaEpXrCkpD22jXi9wbI_zbmIWepRuXrR2YxfbogB"
    test_webhook(webhook_url)