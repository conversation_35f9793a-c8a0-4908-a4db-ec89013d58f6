"""
Image processing utilities for VLife RP Discord ticket panel system.
Handles image cropping, scaling, and processing to maintain fixed embed dimensions.
"""

import io
import logging
import asyncio
from typing import Optional, Tu<PERSON>, Union
from PIL import Image, ImageOps, ImageDraw, ImageFont
import aiohttp
import discord

logger = logging.getLogger(__name__)

class ImageProcessor:
    """Handles image processing for Discord embeds with fixed dimensions"""
    
    # Fixed dimensions for consistent embed layout
    BANNER_WIDTH = 400
    BANNER_HEIGHT = 200
    THUMBNAIL_SIZE = 64
    
    # Default VLife RP themed images (base64 or URLs)
    DEFAULT_BANNER_URL = "https://cdn.discordapp.com/attachments/placeholder/vlife_banner.png"
    DEFAULT_THUMBNAIL_URL = "https://cdn.discordapp.com/attachments/placeholder/vlife_badge.png"
    
    # Fallback colors for generated images
    VLIFE_COLORS = {
        'primary': '#DC143C',    # Crimson
        'secondary': '#2C2C2C',  # Dark gray
        'accent': '#FFD700',     # Gold
        'background': '#1a1a1a'  # Very dark gray
    }
    
    @staticmethod
    async def download_image(url: str, timeout: int = 10) -> Optional[bytes]:
        """Download image from URL with error handling"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=timeout)) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '').lower()
                        if content_type.startswith('image/'):
                            return await response.read()
                        else:
                            logger.warning(f"URL does not contain an image: {url}")
                            return None
                    else:
                        logger.warning(f"Failed to download image: HTTP {response.status}")
                        return None
        except Exception as e:
            logger.error(f"Error downloading image from {url}: {e}")
            return None
    
    @staticmethod
    def process_image_to_banner(image_data: bytes) -> Optional[bytes]:
        """Process image to fixed banner dimensions (400x200px)"""
        try:
            # Open image
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Calculate scaling to maintain aspect ratio
            original_width, original_height = image.size
            target_width, target_height = ImageProcessor.BANNER_WIDTH, ImageProcessor.BANNER_HEIGHT
            
            # Calculate scale factors
            scale_x = target_width / original_width
            scale_y = target_height / original_height
            
            # Use the larger scale factor to ensure image covers the entire area
            scale = max(scale_x, scale_y)
            
            # Calculate new dimensions
            new_width = int(original_width * scale)
            new_height = int(original_height * scale)
            
            # Resize image
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Crop to exact dimensions (center crop)
            left = (new_width - target_width) // 2
            top = (new_height - target_height) // 2
            right = left + target_width
            bottom = top + target_height
            
            image = image.crop((left, top, right, bottom))
            
            # Save to bytes
            output = io.BytesIO()
            image.save(output, format='PNG', optimize=True)
            output.seek(0)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error processing image to banner: {e}")
            return None
    
    @staticmethod
    def process_image_to_thumbnail(image_data: bytes) -> Optional[bytes]:
        """Process image to fixed thumbnail dimensions (64x64px)"""
        try:
            # Open image
            image = Image.open(io.BytesIO(image_data))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Create square thumbnail with smart cropping
            image = ImageOps.fit(
                image, 
                (ImageProcessor.THUMBNAIL_SIZE, ImageProcessor.THUMBNAIL_SIZE), 
                Image.Resampling.LANCZOS,
                centering=(0.5, 0.5)
            )
            
            # Save to bytes
            output = io.BytesIO()
            image.save(output, format='PNG', optimize=True)
            output.seek(0)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error processing image to thumbnail: {e}")
            return None
    
    @staticmethod
    def create_default_banner(text: str = "VLife RP") -> bytes:
        """Create a default VLife RP themed banner"""
        try:
            # Create image with VLife RP colors
            image = Image.new('RGB', (ImageProcessor.BANNER_WIDTH, ImageProcessor.BANNER_HEIGHT), 
                            ImageProcessor.VLIFE_COLORS['background'])
            draw = ImageDraw.Draw(image)
            
            # Add gradient effect (simple)
            for y in range(ImageProcessor.BANNER_HEIGHT):
                alpha = y / ImageProcessor.BANNER_HEIGHT
                color = ImageProcessor._blend_colors(
                    ImageProcessor.VLIFE_COLORS['background'],
                    ImageProcessor.VLIFE_COLORS['secondary'],
                    alpha
                )
                draw.line([(0, y), (ImageProcessor.BANNER_WIDTH, y)], fill=color)
            
            # Add text
            try:
                # Try to use a nice font, fallback to default
                font = ImageFont.truetype("arial.ttf", 36)
            except:
                font = ImageFont.load_default()
            
            # Calculate text position (center)
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (ImageProcessor.BANNER_WIDTH - text_width) // 2
            y = (ImageProcessor.BANNER_HEIGHT - text_height) // 2
            
            # Draw text with outline
            outline_color = ImageProcessor.VLIFE_COLORS['secondary']
            text_color = ImageProcessor.VLIFE_COLORS['accent']
            
            # Draw outline
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    if dx != 0 or dy != 0:
                        draw.text((x + dx, y + dy), text, font=font, fill=outline_color)
            
            # Draw main text
            draw.text((x, y), text, font=font, fill=text_color)
            
            # Save to bytes
            output = io.BytesIO()
            image.save(output, format='PNG', optimize=True)
            output.seek(0)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error creating default banner: {e}")
            # Return a simple colored rectangle as ultimate fallback
            image = Image.new('RGB', (ImageProcessor.BANNER_WIDTH, ImageProcessor.BANNER_HEIGHT), 
                            ImageProcessor.VLIFE_COLORS['primary'])
            output = io.BytesIO()
            image.save(output, format='PNG')
            output.seek(0)
            return output.getvalue()
    
    @staticmethod
    def create_default_thumbnail(text: str = "RP") -> bytes:
        """Create a default VLife RP themed thumbnail"""
        try:
            # Create circular thumbnail
            size = ImageProcessor.THUMBNAIL_SIZE
            image = Image.new('RGB', (size, size), ImageProcessor.VLIFE_COLORS['background'])
            draw = ImageDraw.Draw(image)
            
            # Draw circle
            margin = 2
            draw.ellipse([margin, margin, size-margin, size-margin], 
                        fill=ImageProcessor.VLIFE_COLORS['primary'],
                        outline=ImageProcessor.VLIFE_COLORS['accent'],
                        width=2)
            
            # Add text
            try:
                font = ImageFont.truetype("arial.ttf", 20)
            except:
                font = ImageFont.load_default()
            
            # Calculate text position (center)
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            x = (size - text_width) // 2
            y = (size - text_height) // 2
            
            # Draw text
            draw.text((x, y), text, font=font, fill=ImageProcessor.VLIFE_COLORS['accent'])
            
            # Save to bytes
            output = io.BytesIO()
            image.save(output, format='PNG', optimize=True)
            output.seek(0)
            
            return output.getvalue()
            
        except Exception as e:
            logger.error(f"Error creating default thumbnail: {e}")
            # Return a simple colored circle as ultimate fallback
            image = Image.new('RGB', (ImageProcessor.THUMBNAIL_SIZE, ImageProcessor.THUMBNAIL_SIZE), 
                            ImageProcessor.VLIFE_COLORS['primary'])
            output = io.BytesIO()
            image.save(output, format='PNG')
            output.seek(0)
            return output.getvalue()
    
    @staticmethod
    def _blend_colors(color1: str, color2: str, alpha: float) -> str:
        """Blend two hex colors"""
        try:
            # Convert hex to RGB
            c1 = tuple(int(color1[i:i+2], 16) for i in (1, 3, 5))
            c2 = tuple(int(color2[i:i+2], 16) for i in (1, 3, 5))
            
            # Blend
            blended = tuple(int(c1[i] * (1 - alpha) + c2[i] * alpha) for i in range(3))
            
            # Convert back to hex
            return f"#{blended[0]:02x}{blended[1]:02x}{blended[2]:02x}"
        except:
            return color1
    
    @staticmethod
    async def process_image_for_embed(image_url: str, image_type: str = "banner") -> Optional[discord.File]:
        """
        Process image for Discord embed with fixed dimensions
        
        Args:
            image_url: URL of the image to process
            image_type: "banner" or "thumbnail"
            
        Returns:
            discord.File object ready for embed, or None if processing failed
        """
        try:
            # Download image
            image_data = await ImageProcessor.download_image(image_url)
            
            if image_data:
                # Process based on type
                if image_type == "banner":
                    processed_data = ImageProcessor.process_image_to_banner(image_data)
                    filename = "vlife_banner.png"
                elif image_type == "thumbnail":
                    processed_data = ImageProcessor.process_image_to_thumbnail(image_data)
                    filename = "vlife_thumbnail.png"
                else:
                    logger.error(f"Unknown image type: {image_type}")
                    return None
                
                if processed_data:
                    return discord.File(io.BytesIO(processed_data), filename=filename)
            
            # Fallback to default images
            if image_type == "banner":
                default_data = ImageProcessor.create_default_banner()
                return discord.File(io.BytesIO(default_data), filename="vlife_default_banner.png")
            elif image_type == "thumbnail":
                default_data = ImageProcessor.create_default_thumbnail()
                return discord.File(io.BytesIO(default_data), filename="vlife_default_thumbnail.png")
            
            return None
            
        except Exception as e:
            logger.error(f"Error processing image for embed: {e}")
            return None


class ImageCache:
    """Simple in-memory cache for processed images"""
    
    def __init__(self, max_size: int = 100):
        self.cache = {}
        self.max_size = max_size
        self.access_order = []
    
    def get(self, key: str) -> Optional[bytes]:
        """Get cached image data"""
        if key in self.cache:
            # Move to end (most recently used)
            self.access_order.remove(key)
            self.access_order.append(key)
            return self.cache[key]
        return None
    
    def put(self, key: str, data: bytes):
        """Cache image data"""
        if key in self.cache:
            # Update existing
            self.access_order.remove(key)
        elif len(self.cache) >= self.max_size:
            # Remove least recently used
            oldest = self.access_order.pop(0)
            del self.cache[oldest]
        
        self.cache[key] = data
        self.access_order.append(key)
    
    def clear(self):
        """Clear all cached data"""
        self.cache.clear()
        self.access_order.clear()


# Global image cache instance
image_cache = ImageCache()
