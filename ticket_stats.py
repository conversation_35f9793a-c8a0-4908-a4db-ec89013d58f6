"""
VLife RP Ticket Statistics Manager
Handles real-time ticket statistics with caching and performance optimization
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from database import DatabaseManager
import discord

logger = logging.getLogger(__name__)


class TicketStatsManager:
    """Manages ticket statistics with caching and real-time updates"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.cache = {}
        self.cache_ttl = {}
        self.cache_duration = 300  # 5 minutes cache
        self.update_lock = asyncio.Lock()
    
    async def get_live_statistics(self, guild_id: int) -> Optional[Dict[str, Any]]:
        """Get live ticket statistics for a guild with caching"""
        try:
            # Check cache first
            cache_key = f"stats_{guild_id}"
            now = datetime.now()
            
            if (cache_key in self.cache and 
                cache_key in self.cache_ttl and 
                now < self.cache_ttl[cache_key]):
                return self.cache[cache_key]
            
            # Connect to database
            if not await self.db.connect():
                logger.error("Failed to connect to database for statistics")
                return None
            
            # Calculate statistics
            stats = await self._calculate_statistics(guild_id)
            
            # Cache the results
            if stats:
                self.cache[cache_key] = stats
                self.cache_ttl[cache_key] = now + timedelta(seconds=self.cache_duration)
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting live statistics: {e}")
            return None
    
    async def _calculate_statistics(self, guild_id: int) -> Optional[Dict[str, Any]]:
        """Calculate ticket statistics from database"""
        try:
            tickets_collection = self.db.get_collection("tickets")
            if not tickets_collection:
                return None
            
            now = datetime.now()
            
            # Get current week start (Monday)
            week_start = now - timedelta(days=now.weekday())
            week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Count closed tickets this week
            closed_this_week = await tickets_collection.count_documents({
                "guild_id": guild_id,
                "status": "closed",
                "closed_at": {"$gte": week_start}
            })
            
            # Count active tickets
            active_tickets = await tickets_collection.count_documents({
                "guild_id": guild_id,
                "status": {"$in": ["open", "in_progress", "pending"]}
            })
            
            # Calculate average resolution time (last 30 days)
            thirty_days_ago = now - timedelta(days=30)
            
            pipeline = [
                {
                    "$match": {
                        "guild_id": guild_id,
                        "status": "closed",
                        "closed_at": {"$gte": thirty_days_ago},
                        "created_at": {"$exists": True},
                        "closed_at": {"$exists": True}
                    }
                },
                {
                    "$project": {
                        "resolution_time_hours": {
                            "$divide": [
                                {"$subtract": ["$closed_at", "$created_at"]},
                                1000 * 60 * 60  # Convert milliseconds to hours
                            ]
                        }
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "avg_resolution_hours": {"$avg": "$resolution_time_hours"},
                        "count": {"$sum": 1}
                    }
                }
            ]
            
            avg_result = await tickets_collection.aggregate(pipeline).to_list(1)
            
            if avg_result and avg_result[0].get("avg_resolution_hours") is not None:
                avg_hours = avg_result[0]["avg_resolution_hours"]
                if avg_hours < 1:
                    avg_resolution = f"{int(avg_hours * 60)}m"
                elif avg_hours < 24:
                    avg_resolution = f"{int(avg_hours)}h"
                else:
                    avg_resolution = f"{int(avg_hours / 24)}d"
            else:
                avg_resolution = "N/A"
            
            # Get total tickets created this month
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            tickets_this_month = await tickets_collection.count_documents({
                "guild_id": guild_id,
                "created_at": {"$gte": month_start}
            })
            
            return {
                "closed_this_week": closed_this_week,
                "active_tickets": active_tickets,
                "avg_resolution": avg_resolution,
                "tickets_this_month": tickets_this_month,
                "last_updated": now.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating statistics: {e}")
            return None
    
    async def record_ticket_created(self, guild_id: int, ticket_id: str, category: str, user_id: int):
        """Record a new ticket creation"""
        try:
            if not await self.db.connect():
                return False
            
            tickets_collection = self.db.get_collection("tickets")
            if not tickets_collection:
                return False
            
            ticket_data = {
                "guild_id": guild_id,
                "ticket_id": ticket_id,
                "category": category,
                "user_id": user_id,
                "status": "open",
                "created_at": datetime.now(),
                "last_activity": datetime.now()
            }
            
            await tickets_collection.insert_one(ticket_data)
            
            # Invalidate cache
            await self._invalidate_cache(guild_id)
            
            logger.info(f"Recorded ticket creation: {ticket_id} in guild {guild_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error recording ticket creation: {e}")
            return False
    
    async def record_ticket_closed(self, guild_id: int, ticket_id: str, closed_by_id: int):
        """Record a ticket closure"""
        try:
            if not await self.db.connect():
                return False
            
            tickets_collection = self.db.get_collection("tickets")
            if not tickets_collection:
                return False
            
            result = await tickets_collection.update_one(
                {"guild_id": guild_id, "ticket_id": ticket_id},
                {
                    "$set": {
                        "status": "closed",
                        "closed_at": datetime.now(),
                        "closed_by": closed_by_id
                    }
                }
            )
            
            if result.modified_count > 0:
                # Invalidate cache
                await self._invalidate_cache(guild_id)
                logger.info(f"Recorded ticket closure: {ticket_id} in guild {guild_id}")
                return True
            else:
                logger.warning(f"Ticket not found for closure: {ticket_id} in guild {guild_id}")
                return False
            
        except Exception as e:
            logger.error(f"Error recording ticket closure: {e}")
            return False
    
    async def update_ticket_status(self, guild_id: int, ticket_id: str, new_status: str):
        """Update ticket status"""
        try:
            if not await self.db.connect():
                return False
            
            tickets_collection = self.db.get_collection("tickets")
            if not tickets_collection:
                return False
            
            result = await tickets_collection.update_one(
                {"guild_id": guild_id, "ticket_id": ticket_id},
                {
                    "$set": {
                        "status": new_status,
                        "last_activity": datetime.now()
                    }
                }
            )
            
            if result.modified_count > 0:
                # Invalidate cache
                await self._invalidate_cache(guild_id)
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating ticket status: {e}")
            return False
    
    async def get_user_active_tickets(self, guild_id: int, user_id: int) -> int:
        """Get count of active tickets for a user"""
        try:
            if not await self.db.connect():
                return 0
            
            tickets_collection = self.db.get_collection("tickets")
            if not tickets_collection:
                return 0
            
            count = await tickets_collection.count_documents({
                "guild_id": guild_id,
                "user_id": user_id,
                "status": {"$in": ["open", "in_progress", "pending"]}
            })
            
            return count
            
        except Exception as e:
            logger.error(f"Error getting user active tickets: {e}")
            return 0
    
    async def _invalidate_cache(self, guild_id: int):
        """Invalidate cache for a guild"""
        cache_key = f"stats_{guild_id}"
        if cache_key in self.cache:
            del self.cache[cache_key]
        if cache_key in self.cache_ttl:
            del self.cache_ttl[cache_key]
    
    async def cleanup_old_tickets(self, days_old: int = 90):
        """Clean up old closed tickets (optional maintenance)"""
        try:
            if not await self.db.connect():
                return 0
            
            tickets_collection = self.db.get_collection("tickets")
            if not tickets_collection:
                return 0
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            result = await tickets_collection.delete_many({
                "status": "closed",
                "closed_at": {"$lt": cutoff_date}
            })
            
            logger.info(f"Cleaned up {result.deleted_count} old tickets")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"Error cleaning up old tickets: {e}")
            return 0
    
    def clear_cache(self):
        """Clear all cached statistics"""
        self.cache.clear()
        self.cache_ttl.clear()


# Global instance
ticket_stats_manager = TicketStatsManager()


async def get_ticket_statistics(guild_id: int) -> Optional[Dict[str, Any]]:
    """Convenience function to get ticket statistics"""
    return await ticket_stats_manager.get_live_statistics(guild_id)


async def record_ticket_event(event_type: str, guild_id: int, ticket_id: str, **kwargs):
    """Record various ticket events"""
    if event_type == "created":
        return await ticket_stats_manager.record_ticket_created(
            guild_id, ticket_id, kwargs.get("category", ""), kwargs.get("user_id", 0)
        )
    elif event_type == "closed":
        return await ticket_stats_manager.record_ticket_closed(
            guild_id, ticket_id, kwargs.get("closed_by_id", 0)
        )
    elif event_type == "status_update":
        return await ticket_stats_manager.update_ticket_status(
            guild_id, ticket_id, kwargs.get("status", "open")
        )
    else:
        logger.warning(f"Unknown ticket event type: {event_type}")
        return False
