# VLife RP Discord Ticket Panel System

A modern, GTA RP-themed Discord ticket panel system with fixed dimensions, button interactions, and live statistics.

## Features

### 🎨 Visual Design
- **Fixed Embed Dimensions**: Consistent 400px width layout that never changes
- **GTA RP Theme**: Dark, neon-lit Los Santos aesthetic with crimson/noir color scheme
- **Smart Image Processing**: Automatic cropping/scaling to maintain consistent proportions
- **Fallback System**: Default VLife RP themed images when no custom images provided

### 🎮 Interactive Elements
- **Button-Based Categories**: Clean button interface replacing dropdown menus
  - 🚨 Report a Player (Red)
  - 💰 Refund Request (Green)
  - 🐛 Bug Report (Gray)
  - 📋 Whitelist Application (Blue)
  - ❓ General Support (Gray)

### 📊 Live Statistics
- ✅ Tickets Closed This Week
- 📁 Active Tickets (real-time)
- ⏳ Average Resolution Time
- Updates every 5 minutes with caching

### 🛠️ Technical Features
- **Image Processing**: PIL-based image manipulation with fixed dimensions
- **Database Integration**: MongoDB with performance optimization
- **Error Handling**: Comprehensive fallback systems
- **Performance**: Async processing with caching

## Installation

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

The system requires:
- `discord.py>=2.0.0`
- `Pillow>=10.0.0`
- `aiohttp>=3.8.0`
- `pymongo>=4.5.0`
- `motor>=3.3.0`

### 2. Database Setup

Ensure MongoDB is running and accessible. The system will automatically create the required collections:
- `tickets` - Ticket data and statistics
- `ticket_stats` - Cached statistics
- `vlife_config` - Configuration settings

### 3. File Structure

Add these files to your Discord bot directory:
```
├── image_utils.py              # Image processing utilities
├── vlife_ticket_panel.py       # Main embed system
├── vlife_ticket_modals.py      # Modal forms
├── ticket_stats.py             # Statistics manager
├── vlife_config.py             # Configuration system
└── test_vlife_system.py        # Test suite
```

### 4. Integration

The system integrates with your existing `tickets.py` by replacing the `create_ticket_panel` function. The integration includes automatic fallback to the legacy system if the VLife RP system fails.

## Usage

### Basic Setup

1. **Set Ticket Channel**: Use your existing ticket setup commands
2. **Create Panel**: The VLife RP panel will be created automatically
3. **Configure Categories**: Ensure you have the following categories configured:
   - Player Report
   - Refund Request  
   - Bug Report
   - Whitelist Application
   - General Support

### Configuration

The system uses a hierarchical configuration system:

```python
from vlife_config import get_vlife_config, update_vlife_config

# Get current configuration
config = await get_vlife_config(guild_id)

# Update configuration
updates = {
    "branding": {
        "title": "🌃 Your Server Support Center"
    },
    "images": {
        "banner_url": "https://your-server.com/banner.png"
    }
}
await update_vlife_config(guild_id, updates)
```

### Custom Images

#### Banner Images (400x200px)
- **Recommended**: 16:10 aspect ratio
- **Processing**: Automatic cropping/scaling to exact dimensions
- **Fallback**: VLife RP themed default banner

#### Thumbnail Images (64x64px)
- **Recommended**: Square images
- **Processing**: Smart cropping with center focus
- **Fallback**: Guild icon or VLife RP badge

### Statistics Integration

The system automatically tracks:
- Ticket creation events
- Ticket closure events
- Resolution times
- User activity

Statistics are cached for 5 minutes and update automatically.

## Testing

Run the comprehensive test suite:

```bash
python test_vlife_system.py
```

The test suite verifies:
- ✅ Image processing functionality
- ✅ Configuration management
- ✅ Statistics system
- ✅ Embed creation
- ✅ Modal forms
- ✅ System integration

## Customization

### Theme Colors

```python
# Update theme colors
theme_updates = {
    "theme": {
        "primary_color": 0xDC143C,    # Crimson
        "secondary_color": 0x2C2C2C,  # Dark gray
        "accent_color": 0xFFD700,     # Gold
    }
}
await update_vlife_config(guild_id, theme_updates)
```

### Category Configuration

```python
# Customize categories
category_updates = {
    "categories": {
        "report_player": {
            "enabled": True,
            "label": "Report Player",
            "emoji": "🚨",
            "style": "danger"
        }
    }
}
await update_vlife_config(guild_id, category_updates)
```

### Support Information

```python
# Update support info
support_updates = {
    "support_info": {
        "hours": "24/7 Support Available",
        "response_time": "Average response: 30 minutes"
    }
}
await update_vlife_config(guild_id, support_updates)
```

## Error Handling

The system includes comprehensive error handling:

1. **Image Processing Failures**: Falls back to default images
2. **Database Connectivity**: Uses cached data or defaults
3. **Import Errors**: Falls back to legacy ticket system
4. **Network Issues**: Graceful degradation with user feedback

## Performance Optimization

- **Image Caching**: Processed images cached in memory
- **Database Indexing**: Optimized queries for statistics
- **Async Processing**: Non-blocking operations
- **Rate Limiting**: Prevents API abuse

## Troubleshooting

### Common Issues

1. **Images Not Loading**
   - Check image URLs are accessible
   - Verify Pillow installation
   - Check logs for processing errors

2. **Statistics Not Updating**
   - Verify MongoDB connection
   - Check database permissions
   - Clear statistics cache

3. **Buttons Not Working**
   - Ensure modal imports are working
   - Check Discord.py version compatibility
   - Verify persistent view setup

### Debug Mode

Enable debug logging:

```python
import logging
logging.getLogger('vlife_ticket').setLevel(logging.DEBUG)
```

## Support

For issues with the VLife RP ticket system:

1. Run the test suite to identify problems
2. Check logs for error messages
3. Verify all dependencies are installed
4. Ensure MongoDB is accessible

## License

This system is designed specifically for VLife RP and GTA RP themed Discord servers. Modify as needed for your server's requirements.
