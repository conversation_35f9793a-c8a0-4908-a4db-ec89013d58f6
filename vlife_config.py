"""
VLife RP Configuration System
Manages configuration for the VLife RP ticket panel system
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from database import DatabaseManager

logger = logging.getLogger(__name__)


class VLifeRPConfigManager:
    """Manages VLife RP ticket panel configuration"""
    
    def __init__(self):
        self.db = DatabaseManager()
        self.config_cache = {}
        
        # Default configuration
        self.default_config = {
            "enabled": True,
            "theme": {
                "primary_color": 0xDC143C,  # Crimson
                "secondary_color": 0x2C2C2C,  # Dark gray
                "accent_color": 0xFFD700,  # Gold
                "success_color": 0x00FF00,  # Green
                "danger_color": 0xFF0000,  # Red
                "warning_color": 0xFFA500  # Orange
            },
            "branding": {
                "title": "🌃 VLife RP Support Center",
                "description": ("⚠️ **Got a problem on the streets of Los Santos?** Whether it's a stolen ride, "
                              "broken system, or corrupt cop, our **Crimson Team** is on patrol to back you up."),
                "team_name": "Crimson Team",
                "footer_text": "VLife RP • Powered by Crimson Team"
            },
            "support_info": {
                "hours": "🕒 **Support Hours**\nMon–Fri: 9:00 AM – 5:00 PM PST\nSat–Sun: Limited Availability",
                "response_time": "⏱️ **Response Time**\nTypically within 24 hours\nPriority tickets: 2-4 hours"
            },
            "images": {
                "banner_url": None,
                "thumbnail_url": None,
                "use_guild_icon": True,
                "fallback_enabled": True
            },
            "statistics": {
                "enabled": True,
                "cache_duration": 300,  # 5 minutes
                "show_live_stats": True
            },
            "categories": {
                "report_player": {
                    "enabled": True,
                    "label": "Report Player",
                    "emoji": "🚨",
                    "style": "danger",
                    "description": "Report rule violations or problematic behavior"
                },
                "refund_request": {
                    "enabled": True,
                    "label": "Refund Request",
                    "emoji": "💰",
                    "style": "success",
                    "description": "Request refunds for lost items or money"
                },
                "bug_report": {
                    "enabled": True,
                    "label": "Bug Report",
                    "emoji": "🐛",
                    "style": "secondary",
                    "description": "Report technical issues or bugs"
                },
                "whitelist_application": {
                    "enabled": True,
                    "label": "Whitelist Application",
                    "emoji": "📋",
                    "style": "primary",
                    "description": "Apply for server whitelist access"
                },
                "general_support": {
                    "enabled": True,
                    "label": "General Support",
                    "emoji": "❓",
                    "style": "secondary",
                    "description": "General questions and assistance"
                }
            },
            "advanced": {
                "image_processing": {
                    "banner_width": 400,
                    "banner_height": 200,
                    "thumbnail_size": 64,
                    "quality": 85,
                    "format": "PNG"
                },
                "performance": {
                    "cache_images": True,
                    "max_image_cache_size": 100,
                    "async_processing": True
                }
            }
        }
    
    async def get_config(self, guild_id: int) -> Dict[str, Any]:
        """Get configuration for a guild"""
        try:
            # Check cache first
            cache_key = f"vlife_config_{guild_id}"
            if cache_key in self.config_cache:
                return self.config_cache[cache_key]
            
            # Connect to database
            if not await self.db.connect():
                logger.warning("Database not available, using default config")
                return self.default_config.copy()
            
            # Get configuration from database
            config_collection = self.db.get_collection("vlife_config")
            if not config_collection:
                return self.default_config.copy()
            
            stored_config = await config_collection.find_one({"guild_id": guild_id})
            
            if stored_config:
                # Merge with defaults to ensure all keys exist
                config = self._merge_configs(self.default_config, stored_config.get("config", {}))
            else:
                config = self.default_config.copy()
            
            # Cache the configuration
            self.config_cache[cache_key] = config
            
            return config
            
        except Exception as e:
            logger.error(f"Error getting VLife RP config: {e}")
            return self.default_config.copy()
    
    async def update_config(self, guild_id: int, config_updates: Dict[str, Any]) -> bool:
        """Update configuration for a guild"""
        try:
            # Connect to database
            if not await self.db.connect():
                logger.error("Database not available for config update")
                return False
            
            config_collection = self.db.get_collection("vlife_config")
            if not config_collection:
                return False
            
            # Get current config
            current_config = await self.get_config(guild_id)
            
            # Merge updates
            updated_config = self._merge_configs(current_config, config_updates)
            
            # Store in database
            await config_collection.update_one(
                {"guild_id": guild_id},
                {
                    "$set": {
                        "guild_id": guild_id,
                        "config": updated_config,
                        "last_updated": datetime.now()
                    }
                },
                upsert=True
            )
            
            # Update cache
            cache_key = f"vlife_config_{guild_id}"
            self.config_cache[cache_key] = updated_config
            
            logger.info(f"Updated VLife RP config for guild {guild_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating VLife RP config: {e}")
            return False
    
    def _merge_configs(self, base: Dict[str, Any], updates: Dict[str, Any]) -> Dict[str, Any]:
        """Recursively merge configuration dictionaries"""
        result = base.copy()
        
        for key, value in updates.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def clear_cache(self, guild_id: Optional[int] = None):
        """Clear configuration cache"""
        if guild_id:
            cache_key = f"vlife_config_{guild_id}"
            if cache_key in self.config_cache:
                del self.config_cache[cache_key]
        else:
            self.config_cache.clear()
    
    async def reset_config(self, guild_id: int) -> bool:
        """Reset configuration to defaults"""
        try:
            if not await self.db.connect():
                return False
            
            config_collection = self.db.get_collection("vlife_config")
            if not config_collection:
                return False
            
            # Delete existing config
            await config_collection.delete_one({"guild_id": guild_id})
            
            # Clear cache
            self.clear_cache(guild_id)
            
            logger.info(f"Reset VLife RP config for guild {guild_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error resetting VLife RP config: {e}")
            return False
    
    async def get_category_config(self, guild_id: int, category_id: str) -> Optional[Dict[str, Any]]:
        """Get configuration for a specific category"""
        try:
            config = await self.get_config(guild_id)
            return config.get("categories", {}).get(category_id)
        except Exception as e:
            logger.error(f"Error getting category config: {e}")
            return None
    
    async def update_category_config(self, guild_id: int, category_id: str, category_config: Dict[str, Any]) -> bool:
        """Update configuration for a specific category"""
        try:
            config_updates = {
                "categories": {
                    category_id: category_config
                }
            }
            return await self.update_config(guild_id, config_updates)
        except Exception as e:
            logger.error(f"Error updating category config: {e}")
            return False
    
    async def is_enabled(self, guild_id: int) -> bool:
        """Check if VLife RP system is enabled for a guild"""
        try:
            config = await self.get_config(guild_id)
            return config.get("enabled", True)
        except Exception as e:
            logger.error(f"Error checking if VLife RP is enabled: {e}")
            return True  # Default to enabled
    
    async def get_theme_colors(self, guild_id: int) -> Dict[str, int]:
        """Get theme colors for a guild"""
        try:
            config = await self.get_config(guild_id)
            return config.get("theme", self.default_config["theme"])
        except Exception as e:
            logger.error(f"Error getting theme colors: {e}")
            return self.default_config["theme"]


# Global configuration manager instance
vlife_config_manager = VLifeRPConfigManager()


# Convenience functions
async def get_vlife_config(guild_id: int) -> Dict[str, Any]:
    """Get VLife RP configuration for a guild"""
    return await vlife_config_manager.get_config(guild_id)


async def update_vlife_config(guild_id: int, config_updates: Dict[str, Any]) -> bool:
    """Update VLife RP configuration for a guild"""
    return await vlife_config_manager.update_config(guild_id, config_updates)


async def is_vlife_enabled(guild_id: int) -> bool:
    """Check if VLife RP system is enabled for a guild"""
    return await vlife_config_manager.is_enabled(guild_id)
