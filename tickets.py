import discord
from discord.ui import <PERSON><PERSON>, <PERSON>, Modal, TextInput
import discord.ui
import asyncio
import json
import os
import traceback
import logging
from datetime import datetime, timezone
from collections import defaultdict, deque
import asyncio
import time
import random
import pymongo
from bson import ObjectId
import re
import aiohttp
from typing import Dict, Any, Optional, Tuple, List
import io

from bot_instance import bot

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('ticket_system')

# MongoDB setup
try:
    mongo_client = pymongo.MongoClient('mongodb://localhost:27017/')
    db = mongo_client['missminutesbot']
    transcript_collection = db['transcripts']
    ticket_config_collection = db['ticket_config']
    print("Successfully connected to MongoDB")
except Exception as e:
    print(f"Error connecting to MongoDB: {e}")
    traceback.print_exc()

class TicketRateLimitHandler:
    def __init__(self):
        self.rate_limits = {}
        self.queues = {}
        self.processing = {}
        self.max_retries = 15  # Increased retry limit
        self.base_delay = 1.0  # Start with 1 second delay
        self.max_delay = 600.0  # Maximum delay of 10 minutes
        self.bulk_queue = asyncio.Queue()
        self.bulk_processing = False
        self.batch_sizes = defaultdict(lambda: 10)  # Default batch size of 10
        self.success_threshold = 5  # Number of successful operations before increasing batch size
        self.success_counts = defaultdict(int)

    async def execute(self, key, coroutine, *args, **kwargs):
        """Execute a coroutine with enhanced rate limit handling"""
        if key not in self.queues:
            self.queues[key] = asyncio.Queue()
            self.processing[key] = False

        await self.queues[key].put((coroutine, args, kwargs))

        if not self.processing[key]:
            self.processing[key] = True
            asyncio.create_task(self._process_queue(key))

    async def execute_bulk(self, key, operations):
        """Handle bulk operations with dynamic batch sizing"""
        for op in operations:
            await self.bulk_queue.put((key, op))

        if not self.bulk_processing:
            self.bulk_processing = True
            asyncio.create_task(self._process_bulk_queue())

    async def _process_bulk_queue(self):
        """Process bulk operations with smart batching"""
        try:
            while not self.bulk_queue.empty():
                key = None
                batch = []
                batch_size = self.batch_sizes[key]

                # Gather batch of operations
                while len(batch) < batch_size and not self.bulk_queue.empty():
                    key, op = await self.bulk_queue.get()
                    batch.append(op)

                if batch:
                    try:
                        # Execute batch with retry logic
                        success = await self._execute_with_backoff(key, batch)

                        # Adjust batch size based on success/failure
                        if success:
                            self.success_counts[key] += 1
                            if self.success_counts[key] >= self.success_threshold:
                                self.batch_sizes[key] = min(50, self.batch_sizes[key] + 5)
                                self.success_counts[key] = 0
                        else:
                            self.batch_sizes[key] = max(1, self.batch_sizes[key] // 2)
                            self.success_counts[key] = 0

                    except Exception as e:
                        print(f"Error in bulk processing: {e}")
                        self.batch_sizes[key] = max(1, self.batch_sizes[key] // 2)

                    # Add delay between batches with jitter
                    jitter = random.uniform(0, 0.1)
                    await asyncio.sleep(1.0 + jitter)

        finally:
            self.bulk_processing = False

    async def _process_queue(self, key):
        """Process queued items with enhanced rate limiting"""
        try:
            while not self.queues[key].empty():
                if key in self.rate_limits:
                    wait_time = self.rate_limits[key] - time.time()
                    if wait_time > 0:
                        jitter = random.uniform(0, 0.1 * wait_time)
                        await asyncio.sleep(wait_time + jitter)

                coroutine, args, kwargs = await self.queues[key].get()

                success = False
                for attempt in range(self.max_retries):
                    try:
                        await coroutine(*args, **kwargs)
                        success = True
                        break
                    except discord.HTTPException as e:
                        if e.status == 429:  # Rate limit
                            retry_after = e.retry_after if hasattr(e, 'retry_after') else None
                            if retry_after is None:
                                retry_after = min(self.max_delay, self.base_delay * (2 ** attempt))

                            jitter = random.uniform(0, 0.1 * retry_after)
                            total_delay = retry_after + jitter

                            self.rate_limits[key] = time.time() + total_delay
                            print(f"Rate limited on {key}, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{self.max_retries})")
                            await asyncio.sleep(total_delay)
                            continue
                        raise
                    except AttributeError as e:
                        if "'str' object has no attribute 'to_dict'" in str(e):
                            # Skip this error - it's a known issue with Discord object serialization
                            logger.debug(f"Skipping serialization error in operation: {e}")
                            break  # Exit retry loop for this specific error
                        else:
                            logger.error(f"AttributeError in operation: {e}")
                            if attempt == self.max_retries - 1:
                                raise
                    except Exception as e:
                        logger.error(f"Error in operation: {e}")
                        if attempt == self.max_retries - 1:
                            raise

                        backoff = min(self.max_delay, self.base_delay * (2 ** attempt))
                        await asyncio.sleep(backoff)

                if success:
                    # Add successful operation delay with jitter
                    jitter = random.uniform(0, 0.1)
                    await asyncio.sleep(0.5 + jitter)

        finally:
            self.processing[key] = False

    async def _execute_with_backoff(self, key, operations):
        """Execute operations with exponential backoff"""
        for attempt in range(self.max_retries):
            try:
                for op in operations:
                    await op()
                return True
            except discord.HTTPException as e:
                if e.status == 429:
                    retry_after = e.retry_after if hasattr(e, 'retry_after') else None
                    if retry_after is None:
                        retry_after = min(self.max_delay, self.base_delay * (2 ** attempt))

                    jitter = random.uniform(0, 0.1 * retry_after)
                    total_delay = retry_after + jitter

                    print(f"Rate limited in bulk operation, waiting {total_delay:.2f}s")
                    await asyncio.sleep(total_delay)
                    continue
                raise
        return False

# Create global instance
ticket_rate_limiter = TicketRateLimitHandler()

# Enhanced Transcript System Classes
class AdvancedTranscriptFormatter:
    """Advanced transcript formatter with rich Discord embed support and professional styling"""

    def __init__(self):
        self.timezone = timezone.utc
        self.max_embed_length = 4096
        self.max_field_length = 1024
        self.max_embed_fields = 25

        # Color scheme for professional black theme
        self.colors = {
            'primary': 0x000000,      # Professional black
            'staff': 0x5865F2,        # Discord blurple for staff
            'user': 0x2b2d31,         # Dark gray for users
            'system': 0x99aab5,       # Light gray for system messages
            'success': 0x57f287,      # Green for success
            'warning': 0xfee75c,      # Yellow for warnings
            'error': 0xed4245         # Red for errors
        }

        # File type icons for attachments
        self.file_icons = {
            'image': '🖼️',
            'video': '🎥',
            'audio': '🎵',
            'document': '📄',
            'archive': '📦',
            'code': '💻',
            'default': '📎'
        }

        # Message type indicators
        self.message_types = {
            'join': '➡️',
            'leave': '⬅️',
            'edit': '✏️',
            'delete': '🗑️',
            'reaction': '👍',
            'command': '⚡',
            'system': '🤖'
        }

    def get_file_icon(self, filename: str) -> str:
        """Get appropriate icon for file type"""
        if not filename:
            return self.file_icons['default']

        ext = filename.lower().split('.')[-1] if '.' in filename else ''

        if ext in ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'bmp']:
            return self.file_icons['image']
        elif ext in ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm']:
            return self.file_icons['video']
        elif ext in ['mp3', 'wav', 'flac', 'aac', 'ogg']:
            return self.file_icons['audio']
        elif ext in ['pdf', 'doc', 'docx', 'txt', 'rtf']:
            return self.file_icons['document']
        elif ext in ['zip', 'rar', '7z', 'tar', 'gz']:
            return self.file_icons['archive']
        elif ext in ['py', 'js', 'html', 'css', 'java', 'cpp', 'c']:
            return self.file_icons['code']
        else:
            return self.file_icons['default']

    def format_timestamp(self, timestamp_str: str, include_timezone: bool = True) -> str:
        """Format timestamp with timezone support"""
        try:
            if not timestamp_str:
                return "Unknown"

            # Parse ISO format timestamp
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))

            # Convert to UTC if not already
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)

            if include_timezone:
                return dt.strftime('%Y-%m-%d %H:%M:%S UTC')
            else:
                return dt.strftime('%Y-%m-%d %H:%M:%S')

        except Exception as e:
            logger.error(f"Error formatting timestamp {timestamp_str}: {e}")
            return timestamp_str or "Unknown"

    def calculate_resolution_time(self, created_at: str, closed_at: str) -> str:
        """Calculate and format resolution time with proper timezone handling"""
        try:
            if not created_at or not closed_at:
                return "N/A"

            # Parse timestamps ensuring timezone consistency
            created = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            closed = datetime.fromisoformat(closed_at.replace('Z', '+00:00'))

            # Ensure both datetimes are timezone-aware
            if created.tzinfo is None:
                created = created.replace(tzinfo=timezone.utc)
            if closed.tzinfo is None:
                closed = closed.replace(tzinfo=timezone.utc)

            delta = closed - created
            total_seconds = int(delta.total_seconds())

            if total_seconds < 0:
                return "Invalid"
            elif total_seconds < 60:
                return f"{total_seconds}s"
            elif total_seconds < 3600:
                minutes = total_seconds // 60
                seconds = total_seconds % 60
                return f"{minutes}m {seconds}s"
            elif total_seconds < 86400:
                hours = total_seconds // 3600
                minutes = (total_seconds % 3600) // 60
                return f"{hours}h {minutes}m"
            else:
                days = total_seconds // 86400
                hours = (total_seconds % 86400) // 3600
                return f"{days}d {hours}h"

        except Exception as e:
            logger.error(f"Error calculating resolution time: {e}")
            return "N/A"

    def get_user_role_color(self, user_roles: List[int], staff_roles: List[int]) -> int:
        """Get color based on user's highest role"""
        if not user_roles or not staff_roles:
            return self.colors['user']

        # Check if user has any staff role
        for role_id in user_roles:
            if role_id in staff_roles:
                return self.colors['staff']

        return self.colors['user']

    def truncate_content(self, content: str, max_length: int = 1000) -> str:
        """Truncate content with ellipsis for embed limits"""
        if not content:
            return ""

        content = str(content).strip()
        if len(content) <= max_length:
            return content

        return content[:max_length-3] + "..."

    def format_message_content(self, content: str, message_type: str = 'default') -> str:
        """Format message content with syntax highlighting and special formatting"""
        if not content:
            return "*No content*"

        # Handle code blocks with syntax highlighting indicators
        if '```' in content:
            # Add code block indicator
            content = f"💻 {content}"

        # Handle mentions and channels
        content = re.sub(r'<@!?(\d+)>', r'@User(\1)', content)
        content = re.sub(r'<#(\d+)>', r'#Channel(\1)', content)
        content = re.sub(r'<@&(\d+)>', r'@Role(\1)', content)

        # Add message type indicator
        if message_type in self.message_types:
            content = f"{self.message_types[message_type]} {content}"

        return self.truncate_content(content, 1000)

    def group_messages_by_user(self, messages: List[Dict[str, Any]], time_threshold: int = 300) -> List[List[Dict[str, Any]]]:
        """Group consecutive messages by the same user within time threshold (seconds)"""
        if not messages:
            return []

        grouped = []
        current_group = []
        last_author = None
        last_timestamp = None

        for message in messages:
            author_id = message.get('author_id')
            timestamp_str = message.get('timestamp', '')

            try:
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            except:
                timestamp = datetime.now(timezone.utc)

            # Check if we should start a new group
            should_group = (
                last_author == author_id and
                last_timestamp and
                (timestamp - last_timestamp).total_seconds() <= time_threshold
            )

            if should_group and current_group:
                current_group.append(message)
            else:
                if current_group:
                    grouped.append(current_group)
                current_group = [message]

            last_author = author_id
            last_timestamp = timestamp

        if current_group:
            grouped.append(current_group)

        return grouped

    async def create_enhanced_transcript_embeds(self, transcript_data: Dict[str, Any], guild: discord.Guild) -> List[discord.Embed]:
        """Create comprehensive Discord embeds for transcript with chronological order and reply context"""
        embeds = []

        try:
            # Extract basic information
            ticket_id = transcript_data.get('ticket_id', 'Unknown')
            messages = transcript_data.get('messages', [])
            staff_roles = ticket_config.get('staff_roles', [])

            # Create header embed with ticket information
            header_embed = discord.Embed(
                title=f"📜 Ticket Transcript - #{ticket_id}",
                description="Professional ticket transcript with comprehensive formatting and analytics",
                color=self.colors['primary'],
                timestamp=datetime.now(timezone.utc)
            )

            # Add ticket metadata
            created_at = self.format_timestamp(transcript_data.get('created_at', ''))
            closed_at = self.format_timestamp(transcript_data.get('closed_at', ''))
            resolution_time = self.calculate_resolution_time(
                transcript_data.get('created_at', ''),
                transcript_data.get('closed_at', '')
            )

            header_embed.add_field(
                name="📊 Ticket Analytics",
                value=(
                    f"**Created:** {created_at}\n"
                    f"**Closed:** {closed_at}\n"
                    f"**Resolution Time:** {resolution_time}\n"
                    f"**Total Messages:** {len(messages)}"
                ),
                inline=True
            )

            # Calculate participant statistics
            participants = set()
            staff_participants = set()
            for msg in messages:
                author_id = msg.get('author_id')
                if author_id:
                    participants.add(author_id)
                    author_roles = msg.get('author_roles', [])
                    if any(role in staff_roles for role in author_roles):
                        staff_participants.add(author_id)

            header_embed.add_field(
                name="👥 Participants",
                value=(
                    f"**Total:** {len(participants)}\n"
                    f"**Staff:** {len(staff_participants)}\n"
                    f"**Users:** {len(participants) - len(staff_participants)}\n"
                    f"**Category:** {transcript_data.get('category', 'Support')}"
                ),
                inline=True
            )

            # Add closure information
            closer_info = transcript_data.get('closed_by', 'Unknown')
            close_reason = transcript_data.get('close_reason', 'No reason provided')

            header_embed.add_field(
                name="🔒 Closure Details",
                value=(
                    f"**Closed By:** {closer_info}\n"
                    f"**Reason:** {self.truncate_content(close_reason, 100)}\n"
                    f"**Server:** {guild.name if guild else 'Unknown'}"
                ),
                inline=False
            )

            # Add footer with dynamic branding
            footer_text = get_dynamic_footer_text(guild)
            header_embed.set_footer(
                text=f"{footer_text} • Transcript ID: {transcript_data.get('_id', 'Unknown')}"
            )

            embeds.append(header_embed)

            # Group messages for better readability
            message_groups = self.group_messages_by_user(messages)

            # Create message embeds with advanced formatting
            current_embed = None
            field_count = 0
            embed_count = 1

            for group in message_groups:
                if not group:
                    continue

                # Get group information
                first_msg = group[0]
                author_name = first_msg.get('author_name', 'Unknown')
                author_id = first_msg.get('author_id')
                author_roles = first_msg.get('author_roles', [])

                # Determine if author is staff
                is_staff = any(role in staff_roles for role in author_roles)

                # Create new embed if needed
                if current_embed is None or field_count >= self.max_embed_fields - 1:
                    if current_embed:
                        embeds.append(current_embed)

                    embed_count += 1
                    current_embed = discord.Embed(
                        title=f"📝 Messages - Part {embed_count - 1}",
                        color=self.colors['staff'] if is_staff else self.colors['user'],
                        timestamp=datetime.now(timezone.utc)
                    )
                    field_count = 0

                # Format message group
                group_content = []
                for msg in group:
                    timestamp = self.format_timestamp(msg.get('timestamp', ''), include_timezone=False)
                    content = self.format_message_content(msg.get('content', ''))

                    # Add attachments
                    attachments = msg.get('attachments', [])
                    if attachments:
                        for attachment in attachments:
                            filename = attachment.split('/')[-1] if '/' in attachment else attachment
                            icon = self.get_file_icon(filename)
                            group_content.append(f"{icon} [{filename}]({attachment})")

                    # Add reactions if any
                    reactions = msg.get('reactions', [])
                    if reactions:
                        reaction_str = " ".join([f"{r.get('emoji', '👍')}{r.get('count', 1)}" for r in reactions])
                        group_content.append(f"👍 {reaction_str}")

                    group_content.append(f"`{timestamp}` {content}")

                # Create field for this message group
                field_name = f"{'👑' if is_staff else '👤'} {author_name}"
                if is_staff:
                    field_name += " (Staff)"

                field_value = "\n".join(group_content)
                field_value = self.truncate_content(field_value, self.max_field_length)

                current_embed.add_field(
                    name=field_name,
                    value=field_value,
                    inline=False
                )

                field_count += 1

            # Add the last embed if it has content
            if current_embed and field_count > 0:
                embeds.append(current_embed)

            # Create summary embed
            if len(embeds) > 1:
                summary_embed = discord.Embed(
                    title="📋 Transcript Summary",
                    description="This transcript has been split into multiple embeds for optimal readability.",
                    color=self.colors['primary']
                )

                summary_embed.add_field(
                    name="📊 Statistics",
                    value=(
                        f"**Total Embeds:** {len(embeds)}\n"
                        f"**Message Groups:** {len(message_groups)}\n"
                        f"**Processing Time:** {datetime.now(timezone.utc).strftime('%H:%M:%S UTC')}"
                    ),
                    inline=True
                )

                summary_embed.add_field(
                    name="🔍 Search & Filter",
                    value=(
                        "Use Discord's search function to find specific messages.\n"
                        "Filter by user mentions or keywords for quick navigation."
                    ),
                    inline=True
                )

                embeds.append(summary_embed)

            return embeds

        except Exception as e:
            logger.error(f"Error creating transcript embeds: {e}")
            traceback.print_exc()

            # Return error embed
            error_embed = discord.Embed(
                title="❌ Transcript Generation Error",
                description=f"An error occurred while generating the transcript: {str(e)}",
                color=self.colors['error']
            )
            return [error_embed]

    async def create_enhanced_text_transcript(self, transcript_data: Dict[str, Any], guild: discord.Guild) -> str:
        """Create comprehensive text transcript with chronological order and reply context"""
        try:
            lines = []

            # Header section
            ticket_id = transcript_data.get('ticket_id', 'Unknown')
            lines.append("=" * 80)
            lines.append(f"TICKET TRANSCRIPT - #{ticket_id}")
            lines.append("=" * 80)
            lines.append("")

            # Metadata section
            lines.append("TICKET INFORMATION:")
            lines.append("-" * 40)
            lines.append(f"Server: {guild.name if guild else 'Unknown'}")
            lines.append(f"Category: {transcript_data.get('category', 'Support')}")
            lines.append(f"Created: {self.format_timestamp(transcript_data.get('created_at', ''))}")
            lines.append(f"Closed: {self.format_timestamp(transcript_data.get('closed_at', ''))}")
            lines.append(f"Resolution Time: {self.calculate_resolution_time(transcript_data.get('created_at', ''), transcript_data.get('closed_at', ''))}")
            lines.append(f"Closed By: {transcript_data.get('closed_by', 'Unknown')}")
            lines.append(f"Close Reason: {transcript_data.get('close_reason', 'No reason provided')}")
            lines.append("")

            # Messages section - preserve chronological order
            messages = transcript_data.get('messages', [])
            staff_roles = ticket_config.get('staff_roles', [])

            lines.append("CONVERSATION HISTORY:")
            lines.append("-" * 40)
            lines.append("")

            # Sort messages by timestamp to ensure chronological order
            sorted_messages = sorted(messages, key=lambda x: x.get('timestamp', ''))

            # Create a lookup for reply context
            message_lookup = {msg.get('message_id'): msg for msg in sorted_messages if msg.get('message_id')}

            for msg_idx, msg in enumerate(sorted_messages):
                if not msg:
                    continue

                author_name = msg.get('author_name', 'Unknown')
                author_roles = msg.get('author_roles', [])
                is_staff = any(role in staff_roles for role in author_roles)
                timestamp = self.format_timestamp(msg.get('timestamp', ''))
                content_raw = msg.get('content', '')
                content = (content_raw or '').strip()  # Handle None values safely

                # Message header with role indicator
                role_indicator = "[STAFF]" if is_staff else "[USER]"
                lines.append(f"{role_indicator} {author_name} - [{timestamp}]")

                # Check for reply context
                reply_to_id = msg.get('reply_to')
                if reply_to_id and reply_to_id in message_lookup:
                    replied_msg = message_lookup[reply_to_id]
                    replied_author = replied_msg.get('author_name', 'Unknown')
                    replied_content = replied_msg.get('content', '')

                    # Truncate replied content for readability
                    if len(replied_content) > 100:
                        replied_content = replied_content[:97] + "..."

                    lines.append(f"  ↳ Replying to {replied_author}: \"{replied_content}\"")

                # Message content
                if content:
                    # Handle multi-line content
                    for line in content.split('\n'):
                        lines.append(f"  {line}")
                else:
                    lines.append("  *No text content*")

                # Add all attachments with direct URLs
                attachments = msg.get('attachments', [])
                if attachments:
                    lines.append("  📎 Attachments:")
                    for attachment in attachments:
                        filename = attachment.split('/')[-1] if '/' in attachment else attachment
                        icon = self.get_file_icon(filename)
                        lines.append(f"    {icon} {filename}")
                        lines.append(f"    🔗 Direct Link: {attachment}")

                # Add reactions
                reactions = msg.get('reactions', [])
                if reactions:
                    reaction_strs = [f"{r.get('emoji', '👍')}({r.get('count', 1)})" for r in reactions]
                    lines.append(f"  👍 Reactions: {', '.join(reaction_strs)}")

                lines.append("")

            # Footer section
            lines.append("=" * 80)
            lines.append(f"Generated: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
            lines.append(f"Transcript ID: {transcript_data.get('_id', 'Unknown')}")
            lines.append(f"Total Messages: {len(sorted_messages)}")
            lines.append(f"Total Participants: {len(set(msg.get('author_id') for msg in sorted_messages if msg.get('author_id')))}")
            lines.append("=" * 80)

            return "\n".join(lines)

        except Exception as e:
            logger.error(f"Error creating text transcript: {e}")
            traceback.print_exc()
            return f"Error generating transcript: {str(e)}"

# Create global formatter instance
transcript_formatter = AdvancedTranscriptFormatter()

# Simplified transcript system without interactive views

# Interactive view system removed for simplified transcript workflow

# Content validation and dimension constraints for fixed panel appearance
class TicketContentValidator:
    """Handles content validation and dimension constraints for consistent ticket panel appearance"""

    # More lenient character limits for flexible content
    MAX_WELCOME_MESSAGE_LENGTH = 500  # Increased from 250
    MAX_TEAM_NAME_LENGTH = 100  # Increased from 50
    MAX_CATEGORY_NAME_LENGTH = 100  # Increased from 50
    MAX_CATEGORY_DESC_LENGTH = 200  # Increased from 100
    MAX_SUPPORT_HOURS_LENGTH = 400  # Increased from 200
    MAX_RESPONSE_TIME_LENGTH = 300  # Increased from 150
    MAX_FOOTER_LENGTH = 200  # Increased from 100
    MAX_TITLE_LENGTH = 100  # Increased from 50
    MAX_DESCRIPTION_LENGTH = 600  # Increased from 300

    # Image constraints for bulletproof visual consistency (lowered for more flexibility)
    MAX_IMAGE_WIDTH = 1200  # Increased from 600
    MAX_IMAGE_HEIGHT = 400  # Increased from 200

    @staticmethod
    def truncate_text(text: str, max_length: int, add_ellipsis: bool = True) -> str:
        """Truncate text to specified length with ellipsis for consistent appearance"""
        if not text:
            return ""

        text = str(text).strip()
        if len(text) <= max_length:
            return text

        if add_ellipsis and max_length > 3:
            return text[:max_length-3] + "..."
        else:
            return text[:max_length]

    @staticmethod
    def validate_image_url(url: str) -> Tuple[bool, str]:
        """Very lenient image URL validation - accepts almost any HTTP/HTTPS URL"""
        if not url:
            return True, ""  # Empty URL is valid (no image)

        # Very basic URL validation - just check for http/https
        if url.startswith(('http://', 'https://')):
            return True, ""  # Accept any HTTP/HTTPS URL

        # If it doesn't start with http/https, reject it
        return False, "URL must start with http:// or https://"

    @staticmethod
    async def validate_and_process_image_url(url: str) -> Tuple[bool, str, Optional[str]]:
        """
        Validate image URL and check accessibility for bulletproof consistency.
        Returns: (is_valid, error_message, processed_url)
        """
        if not url:
            return True, "", None  # Empty URL is valid (no image)

        # First do basic URL validation
        is_valid, error_msg = TicketContentValidator.validate_image_url(url)
        if not is_valid:
            return False, error_msg, None

        # Skip all network validation - just accept any valid HTTP/HTTPS URL
        return True, "", url

    @classmethod
    def get_dimension_guidance(cls) -> str:
        """Get user-friendly guidance about image dimensions"""
        return (
            f"**Image Guidelines (Very Flexible):**\n"
            f"• Recommended: {cls.MAX_IMAGE_WIDTH}px × {cls.MAX_IMAGE_HEIGHT}px or smaller\n"
            f"• Any HTTP/HTTPS image URL is accepted\n"
            f"• Format: PNG, JPG, WebP, GIF, or any image format\n"
            f"• Images will be displayed as full-width banners\n"
            f"• Discord will automatically resize images as needed"
        )

# Create global validator instance
content_validator = TicketContentValidator()

def get_image_size_recommendations() -> Dict[str, str]:
    """Get image size recommendations for user guidance in configuration interface"""
    return {
        'max_width': str(content_validator.MAX_IMAGE_WIDTH),
        'max_height': str(content_validator.MAX_IMAGE_HEIGHT),
        'aspect_ratio': 'Any aspect ratio (flexible)'
    }

def get_dynamic_footer_text(guild: discord.Guild) -> str:
    """Generate dynamic footer text based on server name"""
    if guild and guild.name:
        # Clean server name for professional appearance
        server_name = guild.name.strip()
        return f"Powered by {server_name} Ticket System"
    else:
        # Fallback to generic branding
        return "Powered by Ticket System"

def count_user_open_tickets(user_id: int) -> int:
    """Count how many open tickets a user currently has"""
    count = 0
    for ticket_data in active_tickets.values():
        if (ticket_data.get("user_id") == user_id and
            ticket_data.get("status", "open") == "open"):
            count += 1
    return count

async def get_ticket_statistics(guild_id: int) -> Dict[str, Any]:
    """Get live ticket statistics for the panel footer"""
    try:
        # Get current active tickets count
        active_count = len([ticket for ticket in active_tickets.values()
                           if ticket.get("status", "open") == "open"])

        # Get tickets closed this week from MongoDB transcripts
        from datetime import datetime, timedelta
        week_ago = datetime.now() - timedelta(days=7)

        # Query MongoDB for closed tickets this week
        closed_this_week = 0
        avg_resolution_hours = 24  # Default fallback

        try:
            # Query transcripts collection for tickets closed in the last week
            query = {
                "guild_id": guild_id,
                "closed_at": {"$gte": week_ago.isoformat()}
            }

            # Get count of tickets closed this week
            closed_this_week = transcript_collection.count_documents(query)

            # Calculate average resolution time from recent tickets
            pipeline = [
                {"$match": query},
                {"$group": {
                    "_id": None,
                    "avg_resolution_time": {"$avg": "$resolution_time"}
                }}
            ]

            result = list(transcript_collection.aggregate(pipeline))
            if result and result[0].get("avg_resolution_time"):
                # Convert seconds to hours
                avg_resolution_hours = max(1, int(result[0]["avg_resolution_time"] / 3600))

        except Exception as e:
            logger.warning(f"Error querying ticket statistics: {e}")
            # Use fallback values
            pass

        return {
            "active_tickets": active_count,
            "closed_this_week": closed_this_week,
            "avg_resolution_hours": avg_resolution_hours
        }

    except Exception as e:
        logger.error(f"Error getting ticket statistics: {e}")
        return {
            "active_tickets": 0,
            "closed_this_week": 0,
            "avg_resolution_hours": 24
        }

async def validate_and_format_panel_content(content: Dict[str, str]) -> Tuple[Dict[str, str], List[str]]:
    """
    Validate and format all panel content with bulletproof size constraints for consistent appearance.
    Returns: (formatted_content, validation_warnings)
    """
    formatted = {}
    warnings = []

    # Validate and truncate welcome message with new limit
    welcome_msg_raw = content.get("welcome_message", "")
    welcome_msg = (welcome_msg_raw or "").strip()  # Handle None values safely
    if welcome_msg:
        if len(welcome_msg) > content_validator.MAX_WELCOME_MESSAGE_LENGTH:
            warnings.append(f"Welcome message truncated from {len(welcome_msg)} to {content_validator.MAX_WELCOME_MESSAGE_LENGTH} characters")
        formatted["welcome_message"] = content_validator.truncate_text(
            welcome_msg, content_validator.MAX_WELCOME_MESSAGE_LENGTH
        )
    else:
        formatted["welcome_message"] = ""

    # Validate image URL with enhanced async validation
    image_url_raw = content.get("welcome_image_url", "")
    image_url = (image_url_raw or "").strip()  # Handle None values safely
    if image_url:
        is_valid, error_msg, processed_url = await content_validator.validate_and_process_image_url(image_url)
        if is_valid and processed_url:
            formatted["welcome_image_url"] = processed_url
            if error_msg:  # Warning message
                warnings.append(f"🖼️ Image Warning: {error_msg}")
        else:
            logger.warning(f"Invalid image URL provided: {error_msg}")
            formatted["welcome_image_url"] = ""  # Clear invalid URL
            warnings.append(f"❌ Image URL rejected: {error_msg}")
    else:
        formatted["welcome_image_url"] = ""

    # Validate and truncate support team name
    team_name_raw = content.get("support_team_name", "")
    team_name = (team_name_raw or "").strip()  # Handle None values safely
    if team_name:
        if len(team_name) > content_validator.MAX_TEAM_NAME_LENGTH:
            warnings.append(f"Support team name truncated from {len(team_name)} to {content_validator.MAX_TEAM_NAME_LENGTH} characters")
        formatted["support_team_name"] = content_validator.truncate_text(
            team_name, content_validator.MAX_TEAM_NAME_LENGTH
        )
    else:
        formatted["support_team_name"] = ""

    # Validate and truncate custom footer
    footer_raw = content.get("custom_footer", "")
    footer = (footer_raw or "").strip()  # Handle None values safely
    if footer:
        if len(footer) > content_validator.MAX_FOOTER_LENGTH:
            warnings.append(f"Custom footer truncated from {len(footer)} to {content_validator.MAX_FOOTER_LENGTH} characters")
        formatted["custom_footer"] = content_validator.truncate_text(
            footer, content_validator.MAX_FOOTER_LENGTH
        )
    else:
        formatted["custom_footer"] = ""

    # Validate and truncate support hours
    hours_raw = content.get("support_hours", "")
    hours = (hours_raw or "").strip()  # Handle None values safely
    if hours:
        if len(hours) > content_validator.MAX_SUPPORT_HOURS_LENGTH:
            warnings.append(f"Support hours truncated from {len(hours)} to {content_validator.MAX_SUPPORT_HOURS_LENGTH} characters")
        formatted["support_hours"] = content_validator.truncate_text(
            hours, content_validator.MAX_SUPPORT_HOURS_LENGTH
        )
    else:
        formatted["support_hours"] = ""

    # Validate and truncate response time
    response_raw = content.get("response_time", "")
    response = (response_raw or "").strip()  # Handle None values safely
    if response:
        if len(response) > content_validator.MAX_RESPONSE_TIME_LENGTH:
            warnings.append(f"Response time truncated from {len(response)} to {content_validator.MAX_RESPONSE_TIME_LENGTH} characters")
        formatted["response_time"] = content_validator.truncate_text(
            response, content_validator.MAX_RESPONSE_TIME_LENGTH
        )
    else:
        formatted["response_time"] = ""

    return formatted, warnings

async def send_validation_notification(interaction: discord.Interaction, warnings: List[str], success_message: str = None):
    """Send a Discord embed notification with validation warnings and success information"""
    try:
        if warnings:
            # Create warning embed
            embed = discord.Embed(
                title="⚠️ Validation Warnings",
                description="Some issues were found with your input:",
                color=0xff9900  # Orange for warnings
            )

            # Add warnings as fields
            warning_text = "\n".join([f"• {warning}" for warning in warnings])
            embed.add_field(
                name="Issues Found",
                value=warning_text,
                inline=False
            )

            if success_message:
                embed.add_field(
                    name="✅ Action Taken",
                    value=success_message,
                    inline=False
                )

            embed.add_field(
                name="💡 What This Means",
                value=(
                    "• Invalid content was automatically corrected or removed\n"
                    "• Your configuration was saved with valid values only\n"
                    "• The ticket panel will display correctly with fixed dimensions"
                ),
                inline=False
            )

            embed.set_footer(text="These warnings help ensure bulletproof visual consistency")

            # Send as ephemeral message
            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=embed, ephemeral=True)

        elif success_message:
            # Create success embed if no warnings
            embed = discord.Embed(
                title="✅ Success",
                description=success_message,
                color=0x00ff00  # Green for success
            )

            if not interaction.response.is_done():
                await interaction.response.send_message(embed=embed, ephemeral=True)
            else:
                await interaction.followup.send(embed=embed, ephemeral=True)

    except Exception as e:
        logger.error(f"Error sending validation notification: {e}")
        # Fallback to simple text message
        try:
            message = f"⚠️ Validation warnings: {'; '.join(warnings)}" if warnings else success_message
            if not interaction.response.is_done():
                await interaction.response.send_message(message, ephemeral=True)
            else:
                await interaction.followup.send(message, ephemeral=True)
        except:
            pass  # If all else fails, just log it

async def setup_ticket_system(interaction: discord.Interaction):
    """Setup the ticket system with enhanced categories and options"""
    try:
        # Create a professional setup embed
        embed = discord.Embed(
            title="🎫 Ticket System Setup",
            description="Configure your ticket system with the options below. Click the buttons to manage different aspects of the system.",
            color=0x5865F2  # Discord Blurple color for a professional look
        )

        # Add current configuration info
        config_info = []

        # Check for staff roles
        staff_roles_str = "None"
        if ticket_config.get("staff_roles"):
            staff_roles = []
            for role_id in ticket_config["staff_roles"]:
                role = interaction.guild.get_role(role_id)
                if role:
                    staff_roles.append(role.mention)
            if staff_roles:
                staff_roles_str = ", ".join(staff_roles)

        config_info.append(f"**Staff Roles:** {staff_roles_str}")

        # Check for transcript channel
        transcript_channel_str = "None"
        if ticket_config.get("transcript_channel"):
            channel = interaction.guild.get_channel(ticket_config["transcript_channel"])
            if channel:
                transcript_channel_str = channel.mention

        config_info.append(f"**Transcript Channel:** {transcript_channel_str}")

        # Check for ticket channel
        ticket_channel_str = "None"
        if ticket_config.get("ticket_channel"):
            channel = interaction.guild.get_channel(ticket_config["ticket_channel"])
            if channel:
                ticket_channel_str = channel.mention

        config_info.append(f"**Ticket Channel:** {ticket_channel_str}")

        # Check for categories
        categories_str = "None"
        if ticket_config.get("categories"):
            categories = []
            for category_id, details in ticket_config["categories"].items():
                category = interaction.guild.get_channel(int(category_id))
                if category:
                    categories.append(f"{details.get('name', 'Unknown')}")
            if categories:
                categories_str = ", ".join(categories)

        config_info.append(f"**Categories:** {categories_str}")

        # Check for concurrent ticket limit
        max_tickets = ticket_config.get("max_tickets_per_user", 3)
        config_info.append(f"**Max Tickets Per User:** {max_tickets}")

        # Add the configuration info to the embed
        embed.add_field(
            name="Current Configuration",
            value="\n".join(config_info),
            inline=False
        )

        # Create buttons for different setup options
        view = discord.ui.View()

        # Add Category button
        add_category_btn = discord.ui.Button(
            label="Add Category",
            style=discord.ButtonStyle.primary,
            custom_id="add_category",
            emoji="➕"
        )

        # Set Staff Role button
        set_staff_btn = discord.ui.Button(
            label="Set Staff Role",
            style=discord.ButtonStyle.success,
            custom_id="set_staff",
            emoji="👮"
        )

        # Set Transcript Channel button
        set_transcript_btn = discord.ui.Button(
            label="Set Transcript Channel",
            style=discord.ButtonStyle.gray,
            custom_id="set_transcript",
            emoji="📜"
        )

        # Set Ticket Channel button
        set_ticket_channel_btn = discord.ui.Button(
            label="Set Ticket Channel",
            style=discord.ButtonStyle.blurple,
            custom_id="set_ticket_channel",
            emoji="🎫"
        )

        # Remove Category button
        remove_category_btn = discord.ui.Button(
            label="Remove Category",
            style=discord.ButtonStyle.red,
            custom_id="remove_category",
            emoji="🗑️"
        )

        # Configure Ticket Appearance button
        configure_appearance_btn = discord.ui.Button(
            label="Configure Ticket Appearance",
            style=discord.ButtonStyle.primary,
            custom_id="configure_appearance",
            emoji="🎨",
            row=1  # Put on second row
        )

        # Set Ticket Limit button
        set_ticket_limit_btn = discord.ui.Button(
            label="Set Ticket Limit",
            style=discord.ButtonStyle.secondary,
            custom_id="set_ticket_limit",
            emoji="🔢",
            row=1  # Put on second row
        )

        # Define button callbacks
        async def add_category_callback(i: discord.Interaction):
            # Use the new advanced category setup modal
            await i.response.send_modal(AdvancedCategorySetupModal())

        async def set_staff_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Staff Role")
            modal.add_item(discord.ui.TextInput(label="Role ID", placeholder="Enter role ID"))

            async def modal_callback(m_i: discord.Interaction):
                try:
                    role_id = int(modal.children[0].value)
                    success = await set_staff_role(role_id)
                    # Use response.defer() first to prevent timeout
                    await m_i.response.defer(ephemeral=True)
                    await m_i.followup.send(
                        f"Staff role {'added successfully' if success else 'already exists'}",
                        ephemeral=True
                    )
                except ValueError:
                    await m_i.response.send_message("Invalid role ID", ephemeral=True)
                except Exception as e:
                    print(f"Error in set staff modal: {e}")
                    try:
                        await m_i.response.send_message("An error occurred while setting the staff role.", ephemeral=True)
                    except:
                        await m_i.followup.send("An error occurred while setting the staff role.", ephemeral=True)

            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        async def set_transcript_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Transcript Channel")
            modal.add_item(discord.ui.TextInput(label="Channel ID", placeholder="Enter channel ID"))

            async def modal_callback(m_i: discord.Interaction):
                try:
                    channel_id = int(modal.children[0].value)
                    await set_transcript_channel(channel_id)
                    await m_i.response.send_message("Transcript channel set successfully!", ephemeral=True)
                except ValueError:
                    await m_i.response.send_message("Invalid channel ID", ephemeral=True)

            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        async def set_ticket_channel_callback(i: discord.Interaction):
            modal = discord.ui.Modal(title="Set Ticket Channel")
            modal.add_item(discord.ui.TextInput(label="Channel ID", placeholder="Enter channel ID"))

            async def modal_callback(m_i: discord.Interaction):
                try:
                    await m_i.response.defer(ephemeral=True)
                    channel_id = int(modal.children[0].value)
                    channel = m_i.guild.get_channel(channel_id)
                    if not channel:
                        await m_i.followup.send("Channel not found!", ephemeral=True)
                        return

                    await set_ticket_channel(channel_id)
                    await create_ticket_panel(channel)
                    await m_i.followup.send("Ticket channel set and panel created successfully!", ephemeral=True)
                except ValueError:
                    await m_i.followup.send("Invalid channel ID", ephemeral=True)
                except Exception as e:
                    print(f"Error in set_ticket_channel modal: {e}")
                    await m_i.followup.send("An error occurred while setting up the ticket channel.", ephemeral=True)

            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        async def remove_category_callback(i: discord.Interaction):
            # Check if there are any categories first
            if not ticket_config.get("categories"):
                await i.response.send_message("No ticket categories exist to remove.", ephemeral=True)
                return

            # Create dropdown with existing categories
            options = [
                discord.SelectOption(
                    label=details["name"],
                    value=str(category_id),  # Ensure category_id is string
                    description=(details.get("description", "") or "")[:100]  # Handle None values safely
                )
                for category_id, details in ticket_config["categories"].items()
            ]

            # Verify we have options before creating the dropdown
            if not options:
                await i.response.send_message("No ticket categories exist to remove.", ephemeral=True)
                return

            select = discord.ui.Select(
                placeholder="Select category to remove",
                options=options
            )

            async def select_callback(interaction: discord.Interaction):
                try:
                    # Defer the response first
                    await interaction.response.defer(ephemeral=True)

                    selected_id = select.values[0]  # Get selected value as string

                    # Check if category exists in config
                    if selected_id not in ticket_config["categories"]:
                        await interaction.followup.send("Category not found.", ephemeral=True)
                        return

                    # Get the category from Discord using int conversion only when needed
                    category = interaction.guild.get_channel(int(selected_id))
                    if category:
                        try:
                            # Delete the category and its channels
                            for channel in category.channels:
                                await channel.delete()
                            await category.delete()
                        except discord.Forbidden:
                            await interaction.followup.send("Missing permissions to delete category.", ephemeral=True)
                            return
                        except Exception as e:
                            await interaction.followup.send(f"Error deleting category: {str(e)}", ephemeral=True)
                            return

                    # Remove from config using string ID
                    del ticket_config["categories"][selected_id]
                    await save_ticket_data()

                    # Update ticket panel if it exists
                    if ticket_config.get("ticket_channel"):
                        channel = interaction.guild.get_channel(ticket_config["ticket_channel"])
                        if channel:
                            await create_ticket_panel(channel)

                    await interaction.followup.send("Category removed successfully!", ephemeral=True)

                except ValueError:
                    await interaction.followup.send("Invalid category ID format.", ephemeral=True)
                except Exception as e:
                    await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)

            select.callback = select_callback

            # Create a new view for the dropdown
            dropdown_view = discord.ui.View()
            dropdown_view.add_item(select)

            await i.response.send_message("Select a category to remove:", view=dropdown_view, ephemeral=True)

        # Define configure appearance callback
        async def configure_appearance_callback(i: discord.Interaction):
            """Show consolidated ticket appearance configuration with modal and recreate panel functionality"""
            await i.response.defer(ephemeral=True)

            # Create embed for appearance configuration with clear explanations
            embed = discord.Embed(
                title="🎨 Configure Ticket Appearance",
                description="Customize all aspects of your ticket system appearance. Configure all settings in one place and immediately apply changes to your ticket panel.",
                color=0x5865F2
            )

            # Add explanations for each setting
            embed.add_field(
                name="Configuration Options",
                value=(
                    "**Welcome Message**: Main text shown in ticket panels\n"
                    "**Welcome Image**: Banner image displayed at the top\n"
                    "**Support Team Name**: Name displayed in ticket headers\n"
                    "**Custom Footer**: Text shown at the bottom of embeds\n"
                    "**Support Hours**: Business hours information\n"
                    "**Response Time**: Expected response time information"
                ),
                inline=False
            )

            # Add current customization info with better descriptions
            custom_settings = []

            if ticket_config.get("welcome_message"):
                preview = ticket_config["welcome_message"][:50] + "..." if len(ticket_config["welcome_message"]) > 50 else ticket_config["welcome_message"]
                custom_settings.append(f"✅ **Welcome Message**: \"{preview}\"")
            else:
                custom_settings.append("❌ **Welcome Message**: Not set (using default)")

            if ticket_config.get("welcome_image_url"):
                custom_settings.append(f"✅ **Welcome Image**: [View Image]({ticket_config['welcome_image_url']})")
            else:
                custom_settings.append("❌ **Welcome Image**: Not set")

            if ticket_config.get("support_team_name"):
                custom_settings.append(f"✅ **Support Team Name**: \"{ticket_config['support_team_name']}\"")
            else:
                custom_settings.append("❌ **Support Team Name**: Not set (using default)")

            if ticket_config.get("custom_footer"):
                custom_settings.append(f"✅ **Custom Footer**: \"{ticket_config['custom_footer']}\"")
            else:
                custom_settings.append("❌ **Custom Footer**: Not set (using default)")

            if ticket_config.get("support_hours"):
                preview = ticket_config["support_hours"][:50] + "..." if len(ticket_config["support_hours"]) > 50 else ticket_config["support_hours"]
                custom_settings.append(f"✅ **Support Hours**: \"{preview}\"")
            else:
                custom_settings.append("❌ **Support Hours**: Not set (using default)")

            if ticket_config.get("response_time"):
                preview = ticket_config["response_time"][:50] + "..." if len(ticket_config["response_time"]) > 50 else ticket_config["response_time"]
                custom_settings.append(f"✅ **Response Time**: \"{preview}\"")
            else:
                custom_settings.append("❌ **Response Time**: Not set (using default)")

            embed.add_field(
                name="Current Settings",
                value="\n".join(custom_settings),
                inline=False
            )

            # Use the existing consolidated appearance view
            view = ConfigureAppearanceView(i.guild)
            await i.followup.send(embed=embed, view=view, ephemeral=True)

        async def set_ticket_limit_callback(i: discord.Interaction):
            """Configure the maximum number of concurrent tickets per user"""
            modal = discord.ui.Modal(title="Set Concurrent Ticket Limit")

            current_limit = ticket_config.get("max_tickets_per_user", 3)
            modal.add_item(discord.ui.TextInput(
                label="Maximum Tickets Per User",
                placeholder="Enter a number between 1 and 10 (recommended: 3-5)",
                default=str(current_limit),
                min_length=1,
                max_length=2
            ))

            async def modal_callback(m_i: discord.Interaction):
                try:
                    new_limit = int(modal.children[0].value)

                    # Validate the limit
                    if new_limit < 1 or new_limit > 10:
                        await m_i.response.send_message(
                            "❌ Invalid limit! Please enter a number between 1 and 10.",
                            ephemeral=True
                        )
                        return

                    # Update the configuration
                    ticket_config["max_tickets_per_user"] = new_limit
                    await save_ticket_data()

                    # Send success embed
                    success_embed = discord.Embed(
                        title="✅ Ticket Limit Updated",
                        description=f"Maximum concurrent tickets per user set to **{new_limit}**",
                        color=0x00ff00
                    )

                    success_embed.add_field(
                        name="What this means",
                        value=(
                            f"• Users can now have up to {new_limit} open tickets simultaneously\n"
                            f"• Users must close existing tickets to create new ones after reaching the limit\n"
                            f"• This helps prevent spam and ensures fair resource usage"
                        ),
                        inline=False
                    )

                    success_embed.set_footer(text=get_dynamic_footer_text(m_i.guild))

                    await m_i.response.send_message(embed=success_embed, ephemeral=True)

                except ValueError:
                    await m_i.response.send_message(
                        "❌ Invalid input! Please enter a valid number.",
                        ephemeral=True
                    )
                except Exception as e:
                    logger.error(f"Error setting ticket limit: {e}")
                    await m_i.response.send_message(
                        "❌ An error occurred while updating the ticket limit.",
                        ephemeral=True
                    )

            modal.on_submit = modal_callback
            await i.response.send_modal(modal)

        # Assign callbacks to buttons
        add_category_btn.callback = add_category_callback
        set_staff_btn.callback = set_staff_callback
        set_transcript_btn.callback = set_transcript_callback
        set_ticket_channel_btn.callback = set_ticket_channel_callback
        remove_category_btn.callback = remove_category_callback
        configure_appearance_btn.callback = configure_appearance_callback
        set_ticket_limit_btn.callback = set_ticket_limit_callback

        # Add buttons to view
        view.add_item(add_category_btn)
        view.add_item(set_staff_btn)
        view.add_item(set_transcript_btn)
        view.add_item(set_ticket_channel_btn)
        view.add_item(remove_category_btn)
        view.add_item(configure_appearance_btn)
        view.add_item(set_ticket_limit_btn)

        # Send the embed with the view
        await interaction.followup.send(embed=embed, view=view)

    except Exception as e:
        logger.error(f"Error in setup_ticket_system: {e}")
        traceback.print_exc()
        await interaction.followup.send("An error occurred while setting up the ticket system.", ephemeral=True)

class CloseConfirmView(discord.ui.View):
	def __init__(self):
		super().__init__(timeout=60)  # 60 second timeout
		self.value = None

	@discord.ui.button(label="Close", style=discord.ButtonStyle.red)
	async def confirm(self, interaction: discord.Interaction, button: discord.ui.Button):
		await interaction.response.defer(ephemeral=True)
		self.value = True
		self.stop()

	@discord.ui.button(label="Cancel", style=discord.ButtonStyle.grey)
	async def cancel(self, interaction: discord.Interaction, button: discord.ui.Button):
		await interaction.response.defer(ephemeral=True)
		self.value = False
		self.stop()

async def modal_callback(m_i: discord.Interaction):
	try:
		channel_id = int(m_i.data["components"][0]["components"][0]["value"])
		channel = m_i.guild.get_channel(channel_id)
		if not channel:
			await m_i.response.send_message("Channel not found!", ephemeral=True)
			return

		success = await set_ticket_channel(channel_id)
		if success:
			try:
				await m_i.response.send_message("Ticket channel set and panel created successfully!", ephemeral=True)
			except discord.errors.NotFound:
				await m_i.followup.send("Ticket channel set and panel created successfully!", ephemeral=True)
		else:
			try:
				await m_i.response.send_message("Failed to set ticket channel", ephemeral=True)
			except discord.errors.NotFound:
				await m_i.followup.send("Failed to set ticket channel", ephemeral=True)
	except ValueError:
		try:
			await m_i.response.send_message("Invalid channel ID", ephemeral=True)
		except discord.errors.NotFound:
			await m_i.followup.send("Invalid channel ID", ephemeral=True)
from discord.ext import commands
from bot_instance import bot
from discord.ui import Button, View, Select
import asyncio
import json
import os
import io
import traceback  # Add this import
from datetime import datetime

class TicketModal(discord.ui.Modal):
	def __init__(self, category_id, ticket_number):
		# Get category name for more professional title
		category_name = "Support"
		category_str_id = str(category_id)
		if category_str_id in ticket_config.get("categories", {}):
			category_name = ticket_config["categories"][category_str_id].get("name", "Support")

		super().__init__(title=f"{category_name} Request")
		self.category_id = category_id
		self.ticket_number = ticket_number

		# Check if this category has custom questions
		category_str_id = str(category_id)

		# Debug output to understand the structure
		print(f"Processing ticket category: {category_id}")
		print(f"Available categories: {ticket_config.get('categories', {})}")

		# Use custom questions if available, otherwise use default questions
		if category_str_id in ticket_config.get("categories", {}):
			category_data = ticket_config["categories"][category_str_id]

			# Check for questions_config first (new format with required/optional settings)
			if "questions_config" in category_data and category_data["questions_config"]:
				questions_config = category_data["questions_config"]

				# Get sorted question keys (q1, q2, q3, etc.)
				question_keys = sorted(questions_config.keys())

				# Discord modals can only have up to 5 text inputs
				max_questions = min(len(question_keys), 5)

				# Add each question to the modal
				for i in range(max_questions):
					key = question_keys[i]
					question_data = questions_config[key]
					question_text = question_data["text"]
					is_required = question_data.get("required", False)

					# For longer questions, we'll format them to fit better in the modal
					if len(question_text) > 45:
						# Create a shortened version for the label
						label = question_text[:42] + "..."
						# Use the full question as the placeholder for context
						placeholder = question_text
					else:
						label = question_text
						placeholder = question_data.get("placeholder", "Please provide your answer here")

					# Add the question to the modal with appropriate styling
					self.add_item(discord.ui.TextInput(
						label=label,
						placeholder=placeholder,
						style=discord.TextStyle.paragraph,
						required=is_required,  # Use the required setting from the config
						min_length=None,  # Allow any length
						max_length=4000   # Maximum Discord allows
					))

			# Fall back to old format if questions_config is not available
			elif "questions" in category_data:
				questions_data = category_data["questions"]

				# Check if questions is a dictionary (like {'q1': 'Question text', 'q2': 'Another question'})
				if isinstance(questions_data, dict):
					# Convert dictionary to list of questions, limiting to 5 (Discord modal limit)
					question_texts = []
					question_keys = sorted(questions_data.keys())[:5]  # Take first 5 keys when sorted

					for key in question_keys:
						question_text = questions_data[key]
						question_texts.append(question_text)

					# Process each question to fit within Discord's constraints
					for i, question_text in enumerate(question_texts):
						# For longer questions, we'll format them to fit better in the modal
						if len(question_text) > 45:
							# Create a shortened version for the label
							label = question_text[:42] + "..."
							# Use the full question as the placeholder for context
							placeholder = question_text
						else:
							label = question_text
							placeholder = "Please provide your answer here"

						# Add the question to the modal with appropriate styling
						self.add_item(discord.ui.TextInput(
							label=label,
							placeholder=placeholder,
							style=discord.TextStyle.paragraph,
							required=i == 0,  # First question is required, others optional
							min_length=None,  # Allow any length
							max_length=4000   # Maximum Discord allows
						))

				# If questions is a list
				elif isinstance(questions_data, list):
					# Process each question (up to 5) to fit within Discord's constraints
					for i, question_text in enumerate(questions_data[:5]):
						# For longer questions, we'll format them to fit better
						if len(question_text) > 45:
							# Create a shortened version for the label
							label = question_text[:42] + "..."
							# Use the full question as the placeholder for context
							placeholder = question_text
						else:
							label = question_text
							placeholder = "Please provide your answer here"

						# Add the question to the modal with appropriate styling
						self.add_item(discord.ui.TextInput(
							label=label,
							placeholder=placeholder,
							style=discord.TextStyle.paragraph,
							required=i == 0,  # First question is required, others optional
							min_length=None,  # Allow any length
							max_length=4000   # Maximum Discord allows
						))
			else:
				# No questions defined, use default professional questions
				self._add_default_questions()
		else:
			# Category not found, use default professional questions
			self._add_default_questions()

	def _add_default_questions(self):
		"""Add default professional questions to the modal"""
		# First question - required
		self.add_item(discord.ui.TextInput(
			label="Please describe your issue",
			placeholder="Provide a detailed description of the problem you're experiencing",
			style=discord.TextStyle.paragraph,
			required=True,
			min_length=10,  # Require at least some detail
			max_length=4000
		))

		# Second question - optional
		self.add_item(discord.ui.TextInput(
			label="Relevant information",
			placeholder="Include any relevant details, screenshots, error messages, or steps to reproduce the issue",
			style=discord.TextStyle.paragraph,
			required=False
		))

		# Third question - optional
		self.add_item(discord.ui.TextInput(
			label="What assistance do you need?",
			placeholder="Let us know what kind of help or resolution you're looking for",
			style=discord.TextStyle.paragraph,
			required=False
		))

	async def on_submit(self, interaction: discord.Interaction):
		try:
			# Get answers from the form
			answers = [child.value for child in self.children]

			# Defer the response immediately to prevent timeout
			await interaction.response.defer(ephemeral=True)

			# Now increment and save the ticket number since we're actually creating the ticket
			async with asyncio.Lock():
				# Double-check the ticket number is still valid and increment it
				current_last_number = ticket_config.get("last_ticket_number", 0)
				if self.ticket_number <= current_last_number:
					# Someone else created a ticket, use the next available number
					self.ticket_number = current_last_number + 1

				# Now officially increment the counter
				ticket_config["last_ticket_number"] = self.ticket_number

				# Save the updated counter immediately
				await save_ticket_data()

			# Define category_str_id at the beginning of the method
			category_str_id = str(self.category_id)

			guild = interaction.guild
			category = guild.get_channel(int(self.category_id))

			if not category:
				await interaction.followup.send(
					"Invalid ticket category! Please contact an administrator.",
					ephemeral=True
				)
				return

			channel_name = f"ticket-{self.ticket_number:04d}"

			channel = await guild.create_text_channel(
				channel_name,
				category=category,
				topic=f"Ticket #{self.ticket_number:04d} | {interaction.user.name}"
			)

			# Set permissions
			await channel.set_permissions(interaction.user, read_messages=True, send_messages=True)
			await channel.set_permissions(guild.default_role, read_messages=False)

			for role_id in ticket_config.get("staff_roles", []):
				role = guild.get_role(role_id)
				if role:
					await channel.set_permissions(role, read_messages=True, send_messages=True)

			# Get category name safely
			category_name = "Support"
			if category_str_id in ticket_config.get("categories", {}):
				category_name = ticket_config["categories"][category_str_id].get("name", "Support")

			# Get category info
			category_info = ticket_config.get("categories", {}).get(category_str_id, {})

			# Get the questions for this category
			questions_list = []

			if category_str_id in ticket_config.get("categories", {}):
				questions_data = ticket_config["categories"][category_str_id].get("questions", [])

				# Handle different question storage formats
				if isinstance(questions_data, dict):
					# Convert dictionary to list of questions
					for key in sorted(questions_data.keys())[:3]:  # Take first 3 keys when sorted
						questions_list.append(questions_data[key])
				elif isinstance(questions_data, list):
					# Use list directly
					questions_list = questions_data[:3]  # Limit to 3 questions

			# If no custom questions, use default ones
			if not questions_list:
				questions_list = [
					"Please describe your issue",
					"Relevant information or evidence",
					"What kind of assistance do you need?"
				]

			# Format all questions and answers
			qa_text = ""
			if len(answers) > 0 and len(questions_list) > 0:
				# Process each question and answer
				for i, question in enumerate(questions_list):
					if i < len(answers):
						answer = answers[i] if answers[i] else "No response"
						qa_text += f"**{question}**\n{answer}\n\n"

			# Get custom welcome message or use default
			welcome_message = ticket_config.get("welcome_message", "Thank you for reaching out to our support team. A staff member will assist you shortly.")

			# Add support team name if configured
			support_team_name = ticket_config.get("support_team_name", "Support Team")

			# Get support hours and response time
			support_hours = ticket_config.get("support_hours", "Monday-Friday: 9AM-5PM\nWeekends: Limited Support")
			response_time = ticket_config.get("response_time", "Our team typically responds within 24 hours during business days.")

			# Add category-specific welcome message if available
			category_welcome = category_info.get("welcome_message", "")

			# Send a separate message with the user mention to properly ping them
			mention_message = await channel.send(f"{interaction.user.mention}")

			# Create a single consolidated professional ticket embed
			ticket_embed = discord.Embed(
				title=f"{support_team_name} Ticket",
				description=(
					f"Thank you for contacting our {support_team_name}.\n\n"
					f"A member of our team will assist you shortly. Please provide any additional information that may help us address your request efficiently."
				),
				color=0x000000  # Pure black color for professional look
			)

			# Add ticket ID with proper formatting
			ticket_embed.add_field(
				name="**Ticket ID**",
				value=f"#{self.ticket_number:04d}",
				inline=False
			)

			# Add elegant separator for visual hierarchy
			ticket_embed.add_field(
				name="\u200b",  # Invisible field name for spacing
				value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
				inline=False
			)

			# Add support hours information with consistent formatting
			ticket_embed.add_field(
				name="**Support Hours**",
				value=support_hours,
				inline=True
			)

			ticket_embed.add_field(
				name="**Expected Response Time**",
				value=response_time,
				inline=True
			)

			# Add elegant separator before request details
			ticket_embed.add_field(
				name="\u200b",  # Invisible field name for spacing
				value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
				inline=False
			)

			# Add Request Details section header with improved formatting
			ticket_embed.add_field(
				name="**Request Details**",
				value="The following information was provided in this support request:",
				inline=False
			)

			# Add fields for each question and answer with improved dark box formatting
			for i, question in enumerate(questions_list):
				if i < len(answers):
					# Format the answer text
					answer_text = answers[i] if answers[i] else "No response provided"

					# Add the field with dark box formatting and proper spacing
					ticket_embed.add_field(
						name=f"**{question}**",
						value=f"```{answer_text}```",  # Use code blocks for dark box effect without extra newline
						inline=False
					)

			# Add elegant separator at the bottom
			ticket_embed.add_field(
				name="\u200b",  # Invisible field name for spacing
				value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
				inline=False
			)

			# Add custom footer with improved formatting
			current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
			footer_text = ticket_config.get("custom_footer", f"Ticket #{self.ticket_number:04d} • Created {current_time}")
			ticket_embed.set_footer(text=footer_text)

			# Create a view with buttons for ticket actions
			view = discord.ui.View(timeout=None)  # Persistent view

			# Create buttons for ticket actions with consistent styling
			close_button = discord.ui.Button(
				label="Close Ticket",
				style=discord.ButtonStyle.secondary,
				custom_id="close_ticket",
				emoji="🔒",
				row=0
			)

			close_reason_button = discord.ui.Button(
				label="Close with Reason",
				style=discord.ButtonStyle.red,
				custom_id="close_with_reason",
				emoji="📝",
				row=0
			)

			# Add buttons to the view
			view.add_item(close_button)
			view.add_item(close_reason_button)

			# Send the single consolidated ticket embed with buttons
			ticket_message = await channel.send(embed=ticket_embed, view=view)

			# Store message IDs including the mention message
			message_ids = [mention_message.id, ticket_message.id]

			# Store ticket info with message IDs
			active_tickets[str(channel.id)] = {
				"user_id": interaction.user.id,
				"ticket_number": self.ticket_number,
				"category_id": self.category_id,
				"mention_message_id": mention_message.id,  # User mention message
				"ticket_message_id": ticket_message.id,  # Main ticket message with consolidated embed and buttons
				"message_ids": message_ids  # All message IDs
			}

			await save_ticket_data()

			# Create simple embed matching the reference style
			creation_embed = discord.Embed(
				title="Ticket",
				description=f"Opened a new ticket. {channel.mention}",
				color=0x00ff00  # Green accent color
			)

			# Add dynamic bot branding footer
			creation_embed.set_footer(text=get_dynamic_footer_text(interaction.guild))

			await interaction.followup.send(embed=creation_embed, ephemeral=True)

		except Exception as e:
			print(f"Error in modal submit: {e}")
			import traceback
			traceback.print_exc()

			try:
				await interaction.followup.send(
					"An error occurred while creating your ticket.",
					ephemeral=True
				)
			except:
				print("Could not send error message")





import io
import random
from datetime import datetime

async def channel_operation_with_backoff(operation, max_retries=5):
	"""Execute channel operations with exponential backoff for rate limits"""
	base_delay = 1.0  # Start with 1 second delay
	max_delay = 600.0  # Maximum delay of 10 minutes

	for attempt in range(max_retries):
		try:
			return await operation()
		except discord.HTTPException as e:
			if e.status == 429:  # Rate limit error
				# Get retry_after from the error if available, otherwise calculate it
				retry_after = e.retry_after if hasattr(e, 'retry_after') else min(base_delay * (2 ** attempt), max_delay)

				# Add jitter to prevent thundering herd
				jitter = random.uniform(0, 0.1 * retry_after)
				total_delay = retry_after + jitter

				print(f"Rate limited on channel operation, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{max_retries})")
				await asyncio.sleep(total_delay)
				continue
			raise  # Re-raise other HTTP exceptions

	raise Exception(f"Failed channel operation after {max_retries} attempts")

async def delete_message_with_backoff(message, max_retries=5):
	"""Delete a message with improved exponential backoff for rate limits"""
	base_delay = 0.1  # Start with 100ms delay
	max_delay = 5.0   # Maximum delay of 5 seconds

	for attempt in range(max_retries):
		try:
			await message.delete()
			return True
		except discord.HTTPException as e:
			if e.status == 429:  # Rate limit error
				# Get retry_after from the error if available, otherwise calculate it
				retry_after = e.retry_after if hasattr(e, 'retry_after') else min(base_delay * (2 ** attempt), max_delay)

				# Add jitter to prevent thundering herd
				jitter = random.uniform(0, 0.1 * retry_after)
				total_delay = retry_after + jitter

				print(f"Rate limited on delete, waiting {total_delay:.2f}s (Attempt {attempt + 1}/{max_retries})")
				await asyncio.sleep(total_delay)
				continue
			elif e.status == 404:  # Message already deleted
				return True
			else:
				print(f"Error deleting message: {e}")
				return False
		except Exception as e:
			print(f"Unexpected error deleting message: {e}")
			return False

	print(f"Failed to delete message after {max_retries} attempts")
	return False

import random
import time




async def perform_channel_operation(operation):
	"""Simple channel operation without rate limit handling"""
	try:
		return await operation()
	except Exception as e:
		print(f"Error in channel operation: {e}")
		return None







# Initialize ticket configuration with enhanced default structure
ticket_config = {
	"categories": {},
	"staff_roles": [],
	"transcript_channel": None,
	"ticket_channel": None,
	"last_ticket_number": 0,
	"welcome_message": None,
	"support_team_name": None,
	"custom_footer": None,
	"panel_message_id": None,
	"support_hours": "Monday-Friday: 9AM-5PM\nWeekends: Limited Support",
	"response_time": "Our team typically responds within 24 hours during business days.",
	"max_tickets_per_user": 3  # Default concurrent ticket limit per user
}

# Active tickets storage
active_tickets = {}
ticket_counter = 0

@bot.event
async def on_ready():
    print(f"{bot.user} has connected to Discord!")
    # Load ticket configuration from MongoDB when bot starts
    await load_ticket_data()



# Duplicate function removed - using the correct version below at line 3079




class AdvancedCategorySetupModal(discord.ui.Modal):
	"""Initial modal for setting up a ticket category before configuring questions"""
	def __init__(self):
		super().__init__(title="Add Ticket Category")

		# Category name input
		self.add_item(discord.ui.TextInput(
			label="Category Name",
			placeholder="e.g., Support Tickets, Bug Reports, Feature Requests",
			required=True,
			max_length=100
		))

		# Category description input
		self.add_item(discord.ui.TextInput(
			label="Category Description",
			placeholder="Brief description of what this category is for",
			style=discord.TextStyle.paragraph,
			required=True,
			max_length=1000
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle initial category setup and proceed to question configuration"""
		name = self.children[0].value
		description = self.children[1].value

		# Create category in Discord
		try:
			# Create the Discord category
			category = await interaction.guild.create_category(name)
			category_id = str(category.id)

			# Store initial category data without questions
			ticket_config["categories"][category_id] = {
				"name": name,
				"description": description,
				"questions_config": {},
				"questions": {}
			}

			# Save to database
			await save_ticket_data()

			# Create a view for question configuration
			view = QuestionConfigView(category_id, name)

			# Send message with the view
			embed = discord.Embed(
				title="Category Created Successfully",
				description=f"The '{name}' ticket category has been created. Now you can configure questions for this category.",
				color=discord.Color.green()
			)

			embed.add_field(
				name="Category ID",
				value=category_id,
				inline=True
			)

			embed.add_field(
				name="Instructions",
				value="Use the buttons below to add, edit, or remove questions. You can add as many questions as needed, set which ones are required, and arrange their order.",
				inline=False
			)

			await interaction.response.send_message(embed=embed, view=view, ephemeral=True)

		except Exception as e:
			logger.error(f"Error creating category: {e}")
			traceback.print_exc()
			await interaction.response.send_message(
				f"Error creating category: {str(e)}",
				ephemeral=True
			)

class QuestionConfigView(discord.ui.View):
	"""View for configuring questions for a ticket category"""
	def __init__(self, category_id, category_name):
		super().__init__(timeout=300)  # 5 minute timeout
		self.category_id = category_id
		self.category_name = category_name
		self.questions = []  # List to store questions

	@discord.ui.button(label="Add Question", style=discord.ButtonStyle.primary, emoji="➕", row=0)
	async def add_question_button(self, interaction: discord.Interaction, button: discord.ui.Button):
		"""Show modal to add a new question"""
		modal = AddQuestionModal(len(self.questions) + 1)
		modal.on_submit_callback = self.add_question_callback
		await interaction.response.send_modal(modal)

	@discord.ui.button(label="Edit Questions", style=discord.ButtonStyle.secondary, emoji="✏️", row=0)
	async def edit_questions_button(self, interaction: discord.Interaction, button: discord.ui.Button):
		"""Show dropdown to select a question to edit"""
		if not self.questions and not ticket_config["categories"][self.category_id].get("questions_config"):
			await interaction.response.send_message("No questions to edit. Add some questions first.", ephemeral=True)
			return

		# Get questions from config if we don't have any in our view yet
		if not self.questions:
			questions_config = ticket_config["categories"][self.category_id].get("questions_config", {})
			for q_id, q_data in questions_config.items():
				self.questions.append({
					"text": q_data["text"],
					"required": q_data["required"],
					"id": q_id
				})

		# Create select menu options
		options = []
		for i, question in enumerate(self.questions):
			required_status = "Required" if question.get("required") else "Optional"
			options.append(
				discord.SelectOption(
					label=f"Question {i+1}",
					description=f"{question['text'][:50]}... ({required_status})",
					value=str(i)
				)
			)

		# Create select menu
		select = discord.ui.Select(
			placeholder="Select a question to edit",
			options=options,
			custom_id="edit_question_select"
		)

		async def select_callback(select_interaction):
			question_index = int(select_interaction.data["values"][0])
			question = self.questions[question_index]

			# Create modal to edit the question
			modal = EditQuestionModal(question_index + 1, question["text"], question.get("required", False))
			modal.on_submit_callback = self.edit_question_callback
			await select_interaction.response.send_modal(modal)

		select.callback = select_callback

		# Create a view for the select menu
		select_view = discord.ui.View(timeout=60)
		select_view.add_item(select)

		await interaction.response.send_message("Select a question to edit:", view=select_view, ephemeral=True)

	@discord.ui.button(label="Remove Question", style=discord.ButtonStyle.danger, emoji="🗑️", row=0)
	async def remove_question_button(self, interaction: discord.Interaction, button: discord.ui.Button):
		"""Show dropdown to select a question to remove"""
		if not self.questions and not ticket_config["categories"][self.category_id].get("questions_config"):
			await interaction.response.send_message("No questions to remove. Add some questions first.", ephemeral=True)
			return

		# Get questions from config if we don't have any in our view yet
		if not self.questions:
			questions_config = ticket_config["categories"][self.category_id].get("questions_config", {})
			for q_id, q_data in questions_config.items():
				self.questions.append({
					"text": q_data["text"],
					"required": q_data["required"],
					"id": q_id
				})

		# Create select menu options
		options = []
		for i, question in enumerate(self.questions):
			required_status = "Required" if question.get("required") else "Optional"
			options.append(
				discord.SelectOption(
					label=f"Question {i+1}",
					description=f"{question['text'][:50]}... ({required_status})",
					value=str(i)
				)
			)

		# Create select menu
		select = discord.ui.Select(
			placeholder="Select a question to remove",
			options=options,
			custom_id="remove_question_select"
		)

		async def select_callback(select_interaction):
			question_index = int(select_interaction.data["values"][0])
			question = self.questions[question_index]

			# Create confirmation view
			confirm_view = discord.ui.View(timeout=60)

			async def confirm_callback(confirm_interaction):
				# Remove the question
				removed_question = self.questions.pop(question_index)

				# Update the category config
				await self.save_questions_to_config()

				await confirm_interaction.response.send_message(
					f"Question removed: '{removed_question['text']}'",
					ephemeral=True
				)

			async def cancel_callback(cancel_interaction):
				await cancel_interaction.response.send_message(
					"Question removal cancelled.",
					ephemeral=True
				)

			# Add confirm/cancel buttons
			confirm_button = discord.ui.Button(
				label="Confirm",
				style=discord.ButtonStyle.danger,
				custom_id="confirm_remove_question"
			)
			confirm_button.callback = confirm_callback

			cancel_button = discord.ui.Button(
				label="Cancel",
				style=discord.ButtonStyle.secondary,
				custom_id="cancel_remove_question"
			)
			cancel_button.callback = cancel_callback

			confirm_view.add_item(confirm_button)
			confirm_view.add_item(cancel_button)

			await select_interaction.response.send_message(
				f"Are you sure you want to remove this question: '{question['text']}'?",
				view=confirm_view,
				ephemeral=True
			)

		select.callback = select_callback

		# Create a view for the select menu
		select_view = discord.ui.View(timeout=60)
		select_view.add_item(select)

		await interaction.response.send_message("Select a question to remove:", view=select_view, ephemeral=True)

	@discord.ui.button(label="Save Configuration", style=discord.ButtonStyle.success, emoji="💾", row=1)
	async def save_button(self, interaction: discord.Interaction, button: discord.ui.Button):
		"""Save the question configuration"""
		if not self.questions:
			await interaction.response.send_message(
				"No questions have been added. Add at least one question before saving.",
				ephemeral=True
			)
			return

		# Save questions to config
		await self.save_questions_to_config()

		# Create summary embed
		embed = discord.Embed(
			title="Question Configuration Saved",
			description=f"Configuration for '{self.category_name}' has been saved with {len(self.questions)} questions.",
			color=discord.Color.green()
		)

		# Add questions to embed
		questions_text = ""
		for i, question in enumerate(self.questions):
			required_status = "Required" if question.get("required") else "Optional"
			questions_text += f"{i+1}. {question['text']} ({required_status})\n"

		embed.add_field(
			name="Questions",
			value=questions_text or "No questions configured",
			inline=False
		)

		# Disable all buttons
		for item in self.children:
			item.disabled = True

		await interaction.response.edit_message(embed=embed, view=self)

	async def add_question_callback(self, interaction, question_text, required):
		"""Callback for when a question is added"""
		# Add the question to our list
		self.questions.append({
			"text": question_text,
			"required": required
		})

		# Update the config
		await self.save_questions_to_config()

		# Send confirmation
		await interaction.response.send_message(
			f"Question added: '{question_text}' (Required: {required})",
			ephemeral=True
		)

	async def edit_question_callback(self, interaction, question_index, question_text, required):
		"""Callback for when a question is edited"""
		# Update the question in our list
		self.questions[question_index - 1] = {
			"text": question_text,
			"required": required
		}

		# Update the config
		await self.save_questions_to_config()

		# Send confirmation
		await interaction.response.send_message(
			f"Question {question_index} updated: '{question_text}' (Required: {required})",
			ephemeral=True
		)

	async def save_questions_to_config(self):
		"""Save the current questions to the ticket config"""
		questions_config = {}
		questions_simple = {}

		for i, question in enumerate(self.questions):
			q_id = f"q{i+1}"
			questions_config[q_id] = {
				"text": question["text"],
				"placeholder": f"Your answer to: {question['text']}",
				"required": question.get("required", False)
			}
			questions_simple[q_id] = question["text"]

		# Update the config
		ticket_config["categories"][self.category_id]["questions_config"] = questions_config
		ticket_config["categories"][self.category_id]["questions"] = questions_simple

		# Save to database
		await save_ticket_data()

		return True

class AddQuestionModal(discord.ui.Modal):
	"""Modal for adding a new question"""
	def __init__(self, question_number):
		super().__init__(title=f"Add Question {question_number}")
		self.question_number = question_number
		self.on_submit_callback = None

		# Question text input
		self.add_item(discord.ui.TextInput(
			label="Question Text",
			placeholder="e.g., Please describe your issue in detail",
			required=True,
			max_length=100
		))

		# Required checkbox (implemented as a select menu since Discord doesn't have checkboxes)
		self.add_item(discord.ui.TextInput(
			label="Required",
			placeholder="Type 'yes' if this question is required, or 'no' if optional",
			required=True,
			max_length=3
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle question submission"""
		question_text = self.children[0].value
		required_input = self.children[1].value.lower()
		required = required_input in ('yes', 'y', 'true', '1')

		if self.on_submit_callback:
			await self.on_submit_callback(interaction, question_text, required)
		else:
			await interaction.response.send_message(
				f"Question added: '{question_text}' (Required: {required})",
				ephemeral=True
			)

class EditQuestionModal(discord.ui.Modal):
	"""Modal for editing an existing question"""
	def __init__(self, question_number, current_text, is_required):
		super().__init__(title=f"Edit Question {question_number}")
		self.question_number = question_number
		self.on_submit_callback = None

		# Question text input
		self.add_item(discord.ui.TextInput(
			label="Question Text",
			placeholder="e.g., Please describe your issue in detail",
			required=True,
			max_length=100,
			default=current_text
		))

		# Required checkbox (implemented as a select menu since Discord doesn't have checkboxes)
		self.add_item(discord.ui.TextInput(
			label="Required",
			placeholder="Type 'yes' if this question is required, or 'no' if optional",
			required=True,
			max_length=3,
			default="yes" if is_required else "no"
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle question update"""
		question_text = self.children[0].value
		required_input = self.children[1].value.lower()
		required = required_input in ('yes', 'y', 'true', '1')

		if self.on_submit_callback:
			await self.on_submit_callback(interaction, self.question_number, question_text, required)
		else:
			await interaction.response.send_message(
				f"Question {self.question_number} updated: '{question_text}' (Required: {required})",
				ephemeral=True
			)

# Keep the original CategoryModal for backward compatibility
class CategoryModal(discord.ui.Modal):
	"""Enhanced modal for adding a ticket category with question configuration"""
	def __init__(self):
		super().__init__(title="Add Ticket Category")

		# Category name input
		self.add_item(discord.ui.TextInput(
			label="Category Name",
			placeholder="e.g., Support Tickets, Bug Reports, Feature Requests",
			required=True,
			max_length=100
		))

		# Category description input
		self.add_item(discord.ui.TextInput(
			label="Category Description",
			placeholder="Brief description of what this category is for",
			style=discord.TextStyle.paragraph,
			required=True,
			max_length=1000
		))

		# Question 1 input (required)
		self.add_item(discord.ui.TextInput(
			label="Question 1 (Required)",
			placeholder="e.g., Please describe your issue in detail",
			required=True,
			max_length=100
		))

		# Question 2 input (optional)
		self.add_item(discord.ui.TextInput(
			label="Question 2 (Optional)",
			placeholder="e.g., What steps have you taken to resolve this?",
			required=False,
			max_length=100
		))

		# Question 3 input (optional)
		self.add_item(discord.ui.TextInput(
			label="Question 3 (Optional)",
			placeholder="e.g., Any additional information we should know?",
			required=False,
			max_length=100
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle category creation with enhanced question configuration"""
		name = self.children[0].value
		description = self.children[1].value

		# Get questions (filter out empty ones)
		questions = {}
		for i, child in enumerate(self.children[2:5], 1):
			if child.value.strip():
				questions[f"q{i}"] = {
					"text": child.value,
					"placeholder": f"Your answer to: {child.value}",
					"required": i == 1  # First question is required
				}

		# Create category in Discord
		try:
			# Create the Discord category
			category = await interaction.guild.create_category(name)

			# Store in config with enhanced data
			ticket_config["categories"][str(category.id)] = {
				"name": name,
				"description": description,
				"questions_config": questions,
				"questions": {f"q{i}": q["text"] for i, q in enumerate(questions.values(), 1)}
			}

			# Save to database
			await save_ticket_data()

			# Send success message with details
			embed = discord.Embed(
				title="Category Created Successfully",
				description=f"The '{name}' ticket category has been created with {len(questions)} question(s).",
				color=discord.Color.green()
			)

			embed.add_field(
				name="Category ID",
				value=str(category.id),
				inline=True
			)

			embed.add_field(
				name="Questions",
				value="\n".join([f"{i}. {q['text']}" for i, q in enumerate(questions.values(), 1)]) if questions else "No custom questions configured",
				inline=False
			)

			await interaction.response.send_message(embed=embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error creating category: {e}")
			traceback.print_exc()
			await interaction.response.send_message(
				f"Error creating category: {str(e)}",
				ephemeral=True
			)


class EditCategoryModal(discord.ui.Modal):
	"""Modal for editing an existing ticket category"""
	def __init__(self, category_id):
		self.category_id = str(category_id)

		# Get current category data
		category_data = ticket_config.get("categories", {}).get(self.category_id, {})
		category_name = category_data.get("name", "")
		category_description = category_data.get("description", "")

		# Get current questions
		questions_config = category_data.get("questions_config", {})
		questions = category_data.get("questions", {})

		super().__init__(title=f"Edit Category: {category_name}")

		# Category name input
		self.add_item(discord.ui.TextInput(
			label="Category Name",
			placeholder="e.g., Support Tickets, Bug Reports",
			required=True,
			max_length=100,
			default=category_name
		))

		# Category description input
		self.add_item(discord.ui.TextInput(
			label="Category Description",
			placeholder="Brief description of what this category is for",
			style=discord.TextStyle.paragraph,
			required=True,
			max_length=1000,
			default=category_description
		))

		# Question 1 input
		q1_text = questions.get("q1", "") or next((q["text"] for q_id, q in questions_config.items() if q_id == "q1"), "")
		self.add_item(discord.ui.TextInput(
			label="Question 1 (Required)",
			placeholder="e.g., Please describe your issue in detail",
			required=True,
			max_length=100,
			default=q1_text
		))

		# Question 2 input
		q2_text = questions.get("q2", "") or next((q["text"] for q_id, q in questions_config.items() if q_id == "q2"), "")
		self.add_item(discord.ui.TextInput(
			label="Question 2 (Optional)",
			placeholder="e.g., What steps have you taken to resolve this?",
			required=False,
			max_length=100,
			default=q2_text
		))

		# Question 3 input
		q3_text = questions.get("q3", "") or next((q["text"] for q_id, q in questions_config.items() if q_id == "q3"), "")
		self.add_item(discord.ui.TextInput(
			label="Question 3 (Optional)",
			placeholder="e.g., Any additional information we should know?",
			required=False,
			max_length=100,
			default=q3_text
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle category update with enhanced question configuration"""
		name = self.children[0].value
		description = self.children[1].value

		# Get questions (filter out empty ones)
		questions = {}
		for i, child in enumerate(self.children[2:5], 1):
			if child.value.strip():
				questions[f"q{i}"] = {
					"text": child.value,
					"placeholder": f"Your answer to: {child.value}",
					"required": i == 1  # First question is required
				}

		try:
			# Update category in Discord if it's a Discord category
			try:
				category_id_int = int(self.category_id)
				category = interaction.guild.get_channel(category_id_int)
				if category:
					await category.edit(name=name)
			except (ValueError, TypeError):
				# Not a Discord category ID or channel not found
				pass

			# Update in config
			if self.category_id in ticket_config.get("categories", {}):
				# Update while preserving other settings
				ticket_config["categories"][self.category_id].update({
					"name": name,
					"description": description,
					"questions_config": questions,
					"questions": {f"q{i}": q["text"] for i, q in enumerate(questions.values(), 1)}
				})

				# Save to database
				await save_ticket_data()

				# Send success message
				embed = discord.Embed(
					title="Category Updated Successfully",
					description=f"The '{name}' ticket category has been updated with {len(questions)} question(s).",
					color=discord.Color.green()
				)

				embed.add_field(
					name="Category ID",
					value=self.category_id,
					inline=True
				)

				embed.add_field(
					name="Questions",
					value="\n".join([f"{i}. {q['text']}" for i, q in enumerate(questions.values(), 1)]) if questions else "No custom questions configured",
					inline=False
				)

				await interaction.response.send_message(embed=embed, ephemeral=True)
			else:
				await interaction.response.send_message(
					"Error: Category not found in configuration.",
					ephemeral=True
				)

		except Exception as e:
			logger.error(f"Error updating category: {e}")
			traceback.print_exc()
			await interaction.response.send_message(
				f"Error updating category: {str(e)}",
				ephemeral=True
			)




class ManageCategoriesView(discord.ui.View):
	"""Enhanced view for managing ticket categories"""
	def __init__(self):
		super().__init__(timeout=180)  # 3 minute timeout
		self.update_options()

	def update_options(self):
		# Clear existing items
		self.clear_items()

		# Add category button with improved styling
		add_button = discord.ui.Button(
			label="Add Category",
			style=discord.ButtonStyle.success,
			custom_id="add_category",
			emoji="➕"
		)
		add_button.callback = self.add_category_callback
		self.add_item(add_button)

		# Add edit category button
		edit_button = discord.ui.Button(
			label="Edit Category",
			style=discord.ButtonStyle.primary,
			custom_id="edit_category",
			emoji="✏️"
		)
		edit_button.callback = self.edit_category_callback
		self.add_item(edit_button)

		# Add remove buttons for each category with improved styling
		for cat_id, info in ticket_config.get("categories", {}).items():
			remove_button = discord.ui.Button(
				label=f"Remove {info['name']}",
				style=discord.ButtonStyle.danger,
				custom_id=f"remove_category_{cat_id}",
				emoji="🗑️"
			)
			remove_button.callback = self.remove_category_callback
			self.add_item(remove_button)

		# Add back button
		back_button = discord.ui.Button(
			label="Back to Setup",
			style=discord.ButtonStyle.secondary,
			custom_id="back_to_setup",
			emoji="◀️"
		)
		back_button.callback = self.back_to_setup_callback
		self.add_item(back_button)

	async def add_category_callback(self, interaction: discord.Interaction):
		"""Show modal to add a new category"""
		await interaction.response.send_modal(CategoryModal())

	async def edit_category_callback(self, interaction: discord.Interaction):
		"""Show dropdown to select a category to edit"""
		# Create a select menu with all categories
		categories = ticket_config.get("categories", {})
		if not categories:
			await interaction.response.send_message(
				"No categories available to edit. Please add a category first.",
				ephemeral=True
			)
			return

		# Create select menu options
		options = []
		for cat_id, info in categories.items():
			options.append(
				discord.SelectOption(
					label=info.get("name", "Unnamed Category"),
					description=f"Edit {info.get('name', 'Unnamed Category')} settings",
					value=cat_id
				)
			)

		# Create select menu view
		view = discord.ui.View(timeout=60)
		select = discord.ui.Select(
			placeholder="Select a category to edit",
			options=options,
			custom_id="edit_category_select"
		)

		async def select_callback(select_interaction):
			category_id = select_interaction.data["values"][0]
			await select_interaction.response.send_modal(
				EditCategoryModal(category_id)
			)

		select.callback = select_callback
		view.add_item(select)

		await interaction.response.send_message(
			"Select a category to edit:",
			view=view,
			ephemeral=True
		)

	async def remove_category_callback(self, interaction: discord.Interaction):
		"""Remove a category with confirmation"""
		category_id = interaction.data["custom_id"].split("_")[-1]

		# Convert to int if it's a Discord category ID
		try:
			category_id = int(category_id)
		except ValueError:
			# If it's not an integer, keep it as a string (for custom categories)
			pass

		if str(category_id) in ticket_config.get("categories", {}):
			# Get category name for confirmation message
			category_name = ticket_config["categories"][str(category_id)].get("name", "Unknown")

			# Create confirmation view
			view = discord.ui.View(timeout=60)

			async def confirm_callback(confirm_interaction):
				# Delete Discord category if it exists
				category = interaction.guild.get_channel(category_id) if isinstance(category_id, int) else None
				if category:
					try:
						await category.delete()
					except Exception as e:
						logger.warning(f"Could not delete category channel: {e}")
						# Continue anyway - this is not critical

				# Remove from config
				del ticket_config["categories"][str(category_id)]
				await save_ticket_data()

				# Update view
				self.update_options()
				await interaction.edit_original_response(view=self)
				await confirm_interaction.response.send_message(
					f"Category '{category_name}' removed successfully!",
					ephemeral=True
				)

			async def cancel_callback(cancel_interaction):
				await cancel_interaction.response.send_message(
					"Category removal cancelled.",
					ephemeral=True
				)

			# Add confirm/cancel buttons
			confirm_button = discord.ui.Button(
				label="Confirm",
				style=discord.ButtonStyle.danger,
				custom_id="confirm_remove"
			)
			confirm_button.callback = confirm_callback

			cancel_button = discord.ui.Button(
				label="Cancel",
				style=discord.ButtonStyle.secondary,
				custom_id="cancel_remove"
			)
			cancel_button.callback = cancel_callback

			view.add_item(confirm_button)
			view.add_item(cancel_button)

			await interaction.response.send_message(
				f"Are you sure you want to remove the '{category_name}' category? This cannot be undone.",
				view=view,
				ephemeral=True
			)

	async def back_to_setup_callback(self, interaction: discord.Interaction):
		"""Go back to the main setup view"""
		await interaction.response.defer(ephemeral=True)
		await setup_ticket_system(interaction)


# Active tickets storage
active_tickets = {}

class StaffRolesView(discord.ui.View):
	"""View for managing staff roles that can access tickets"""
	def __init__(self, guild):
		super().__init__(timeout=180)  # 3 minute timeout
		self.guild = guild

		# Add role button
		add_role_button = discord.ui.Button(
			label="Add Role",
			style=discord.ButtonStyle.success,
			custom_id="add_staff_role",
			emoji="➕"
		)
		add_role_button.callback = self.add_role_callback
		self.add_item(add_role_button)

		# Remove role button
		remove_role_button = discord.ui.Button(
			label="Remove Role",
			style=discord.ButtonStyle.danger,
			custom_id="remove_staff_role",
			emoji="➖"
		)
		remove_role_button.callback = self.remove_role_callback
		self.add_item(remove_role_button)

		# Back button
		back_button = discord.ui.Button(
			label="Back to Setup",
			style=discord.ButtonStyle.secondary,
			custom_id="back_to_setup",
			emoji="◀️"
		)
		back_button.callback = self.back_to_setup_callback
		self.add_item(back_button)

	async def add_role_callback(self, interaction: discord.Interaction):
		"""Show modal to add a staff role"""
		# Create a modal for role ID input
		modal = discord.ui.Modal(title="Add Staff Role")

		# Add text input for role ID
		role_input = discord.ui.TextInput(
			label="Role ID",
			placeholder="Enter the ID of the role to add as staff",
			required=True
		)
		modal.add_item(role_input)

		# Define the callback for when the modal is submitted
		async def modal_callback(modal_interaction: discord.Interaction):
			try:
				role_id = int(role_input.value)
				role = modal_interaction.guild.get_role(role_id)

				if not role:
					await modal_interaction.response.send_message(
						"Role not found! Please check the ID and try again.",
						ephemeral=True
					)
					return

				# Check if role is already in staff roles
				if role_id in ticket_config.get("staff_roles", []):
					await modal_interaction.response.send_message(
						f"The role {role.mention} is already a staff role.",
						ephemeral=True
					)
					return

				# Add role to staff roles
				if "staff_roles" not in ticket_config:
					ticket_config["staff_roles"] = []

				ticket_config["staff_roles"].append(role_id)
				await save_ticket_data()

				await modal_interaction.response.send_message(
					f"Added {role.mention} to staff roles. This role can now access all tickets.",
					ephemeral=True
				)

			except ValueError:
				await modal_interaction.response.send_message(
					"Invalid role ID. Please enter a valid number.",
					ephemeral=True
				)
			except Exception as e:
				logger.error(f"Error adding staff role: {e}")
				traceback.print_exc()
				await modal_interaction.response.send_message(
					f"An error occurred: {str(e)}",
					ephemeral=True
				)

		modal.on_submit = modal_callback
		await interaction.response.send_modal(modal)

	async def remove_role_callback(self, interaction: discord.Interaction):
		"""Show dropdown to select a role to remove"""
		await interaction.response.defer(ephemeral=True)

		# Get current staff roles
		staff_roles = []
		for role_id in ticket_config.get("staff_roles", []):
			role = interaction.guild.get_role(role_id)
			if role:
				staff_roles.append((role_id, role))

		if not staff_roles:
			await interaction.followup.send(
				"No staff roles to remove.",
				ephemeral=True
			)
			return

		# Create select menu options
		options = []
		for role_id, role in staff_roles:
			options.append(
				discord.SelectOption(
					label=role.name,
					description=f"Role ID: {role_id}",
					value=str(role_id)
				)
			)

		# Create select menu view
		view = discord.ui.View(timeout=60)
		select = discord.ui.Select(
			placeholder="Select a role to remove",
			options=options,
			custom_id="remove_staff_role_select"
		)

		async def select_callback(select_interaction):
			role_id = int(select_interaction.data["values"][0])

			# Remove role from staff roles
			if role_id in ticket_config.get("staff_roles", []):
				ticket_config["staff_roles"].remove(role_id)
				await save_ticket_data()

				role = select_interaction.guild.get_role(role_id)
				role_name = role.name if role else f"Role ID: {role_id}"

				await select_interaction.response.send_message(
					f"Removed {role_name} from staff roles.",
					ephemeral=True
				)
			else:
				await select_interaction.response.send_message(
					"This role is not in the staff roles list.",
					ephemeral=True
				)

		select.callback = select_callback
		view.add_item(select)

		await interaction.followup.send(
			"Select a role to remove from staff:",
			view=view,
			ephemeral=True
		)

	async def back_to_setup_callback(self, interaction: discord.Interaction):
		"""Go back to the main setup view"""
		await interaction.response.defer(ephemeral=True)
		await setup_ticket_system(interaction)











class ConfigureAppearanceView(discord.ui.View):
	"""Consolidated view for configuring ticket appearance with modal and recreate panel functionality"""
	def __init__(self, guild):
		super().__init__(timeout=180)  # 3 minute timeout
		self.guild = guild

		# Configure All Settings button (opens consolidated modal)
		configure_all_button = discord.ui.Button(
			label="Configure All Settings",
			style=discord.ButtonStyle.primary,
			custom_id="configure_all_settings",
			emoji="⚙️",
			row=0
		)
		configure_all_button.callback = self.configure_all_callback
		self.add_item(configure_all_button)

		# Recreate Ticket Panel button
		recreate_panel_button = discord.ui.Button(
			label="Recreate Ticket Panel",
			style=discord.ButtonStyle.success,
			custom_id="recreate_panel",
			emoji="🔄",
			row=0
		)
		recreate_panel_button.callback = self.recreate_panel_callback
		self.add_item(recreate_panel_button)

		# Back button
		back_button = discord.ui.Button(
			label="Back to Setup",
			style=discord.ButtonStyle.secondary,
			custom_id="back_to_setup",
			emoji="◀️",
			row=1
		)
		back_button.callback = self.back_to_setup_callback
		self.add_item(back_button)

	async def configure_all_callback(self, interaction: discord.Interaction):
		"""Open consolidated modal with all 6 customization options"""
		modal = ConsolidatedAppearanceModal()
		await interaction.response.send_modal(modal)

	async def recreate_panel_callback(self, interaction: discord.Interaction):
		"""Recreate the ticket panel with current settings"""
		await interaction.response.defer(ephemeral=True)

		# Check if ticket channel is set
		if not ticket_config.get("ticket_channel"):
			await interaction.followup.send(
				"❌ No ticket channel configured. Please set a ticket channel first using 'Set Ticket Channel' in the main setup.",
				ephemeral=True
			)
			return

		# Get the channel
		channel = self.guild.get_channel(ticket_config["ticket_channel"])
		if not channel:
			await interaction.followup.send(
				"❌ The configured ticket channel no longer exists. Please set a new one in the main setup.",
				ephemeral=True
			)
			return

		# Recreate the ticket panel
		try:
			success = await create_ticket_panel(channel)
			if success:
				# Check for validation warnings from panel creation
				validation_warnings = ticket_config.get("_last_validation_warnings", [])

				if validation_warnings:
					# Create embed with success message and warnings
					embed = discord.Embed(
						title="✅ Ticket Panel Recreated Successfully!",
						description=f"Panel recreated in {channel.mention} with your current appearance settings.",
						color=0xff9900  # Orange for warnings
					)
					embed.add_field(
						name="⚠️ Validation Warnings",
						value="\n".join([f"• {warning}" for warning in validation_warnings]),
						inline=False
					)
					embed.add_field(
						name="💡 What This Means",
						value=(
							"• Invalid content was automatically corrected\n"
							"• Your panel displays correctly with fixed dimensions\n"
							"• Consider updating your configuration to fix these issues"
						),
						inline=False
					)
					embed.set_footer(text="Use 'Configure Ticket Appearance' to fix these warnings")
					await interaction.followup.send(embed=embed, ephemeral=True)

					# Clear the warnings after showing them
					if "_last_validation_warnings" in ticket_config:
						del ticket_config["_last_validation_warnings"]
				else:
					# Simple success message if no warnings
					embed = discord.Embed(
						title="✅ Ticket Panel Recreated Successfully!",
						description=f"Panel recreated in {channel.mention} with your current appearance settings.",
						color=0x00ff00  # Green for success
					)
					await interaction.followup.send(embed=embed, ephemeral=True)
			else:
				error_embed = discord.Embed(
					title="❌ Panel Recreation Failed",
					description="There was an error recreating the ticket panel.",
					color=0xff0000  # Red for error
				)
				error_embed.add_field(
					name="What to do",
					value=(
						"• Check the bot's permissions in the ticket channel\n"
						"• Verify the ticket channel still exists\n"
						"• Check the console logs for detailed error information\n"
						"• Try again or contact an administrator"
					),
					inline=False
				)
				await interaction.followup.send(embed=error_embed, ephemeral=True)
		except Exception as e:
			logger.error(f"Error recreating ticket panel: {e}")
			error_embed = discord.Embed(
				title="❌ Unexpected Error",
				description="An unexpected error occurred while recreating the ticket panel.",
				color=0xff0000  # Red for error
			)
			error_embed.add_field(
				name="Error Details",
				value=f"```{str(e)}```",
				inline=False
			)
			error_embed.set_footer(text="Error logged for debugging purposes")
			await interaction.followup.send(embed=error_embed, ephemeral=True)

	async def back_to_setup_callback(self, interaction: discord.Interaction):
		"""Go back to the main setup view"""
		await interaction.response.defer(ephemeral=True)
		await setup_ticket_system(interaction)


class ConsolidatedAppearanceModal(discord.ui.Modal):
	"""Consolidated modal containing all 6 ticket appearance customization options with size constraints"""
	def __init__(self):
		super().__init__(title="Configure Ticket Appearance")

		# Get image size recommendations for placeholder text
		recommendations = get_image_size_recommendations()

		# Welcome Message (Text Input 1) - Updated for more flexibility
		welcome_msg_default = "Thank you for contacting our support team. We've received your request."
		welcome_msg_raw = ticket_config.get("welcome_message", welcome_msg_default)
		welcome_msg_safe = (welcome_msg_raw or welcome_msg_default)[:500]  # Handle None values safely
		self.add_item(discord.ui.TextInput(
			label="Welcome Message",
			placeholder="Message shown when a ticket is created (max 500 chars - flexible length)",
			style=discord.TextStyle.paragraph,
			required=False,
			max_length=500,  # Increased for more flexibility
			default=welcome_msg_safe
		))

		# Welcome Image URL (Text Input 2) with enhanced validation info
		welcome_image_raw = ticket_config.get("welcome_image_url", "")
		welcome_image_safe = welcome_image_raw or ""  # Handle None values safely
		self.add_item(discord.ui.TextInput(
			label="Welcome Image URL",
			placeholder=f"Any HTTP/HTTPS image URL - flexible sizing (recommended: {recommendations['max_width']}x{recommendations['max_height']}px)",
			required=False,
			max_length=500,
			default=welcome_image_safe
		))

		# Support Team Name (Text Input 3) - Updated constraints
		team_name_default = "Support Team"
		team_name_raw = ticket_config.get("support_team_name", team_name_default)
		team_name_safe = (team_name_raw or team_name_default)[:100]  # Handle None values safely
		self.add_item(discord.ui.TextInput(
			label="Support Team Name",
			placeholder="e.g., Technical Support, Customer Service (max 100 chars)",
			required=False,
			max_length=100,  # Increased for more flexibility
			default=team_name_safe
		))

		# Custom Footer (Text Input 4)
		footer_default = f"© {datetime.now().year} Support System • All Rights Reserved"
		footer_raw = ticket_config.get("custom_footer", footer_default)
		footer_safe = (footer_raw or footer_default)[:80]  # Handle None values safely
		self.add_item(discord.ui.TextInput(
			label="Custom Footer",
			placeholder="Text shown at the bottom of ticket embeds (max 80 chars)",
			required=False,
			max_length=80,  # Fixed length constraint
			default=footer_safe
		))

		# Support Hours (Text Input 5) - Note: Discord modals only support 5 text inputs max
		hours_default = 'Monday-Friday: 9AM-5PM'
		response_default = 'Within 24 hours during business days'
		current_hours_raw = ticket_config.get('support_hours', hours_default)
		current_response_raw = ticket_config.get('response_time', response_default)
		current_hours = (current_hours_raw or hours_default)[:100]  # Handle None values safely
		current_response = (current_response_raw or response_default)[:100]  # Handle None values safely
		self.add_item(discord.ui.TextInput(
			label="Support Hours & Response Time",
			placeholder="Format: Hours: [hours] | Response: [time] (max 200 chars total)",
			style=discord.TextStyle.paragraph,
			required=False,
			max_length=200,  # Fixed length constraint
			default=f"Hours: {current_hours} | Response: {current_response}"[:200]
		))

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle the consolidated form submission with validation and size constraints"""
		try:
			await interaction.response.defer(ephemeral=True)

			# Extract and validate values from form inputs
			welcome_message = self.children[0].value.strip()
			welcome_image_url = self.children[1].value.strip()
			support_team_name = self.children[2].value.strip()
			custom_footer = self.children[3].value.strip()
			hours_and_response = self.children[4].value.strip()

			# Validate and format content using the validation functions
			content_to_validate = {
				"welcome_message": welcome_message,
				"welcome_image_url": welcome_image_url,
				"support_team_name": support_team_name,
				"custom_footer": custom_footer,
				"support_hours": "",  # Will be parsed below
				"response_time": ""   # Will be parsed below
			}

			# Parse the combined support hours and response time with improved logic
			support_hours = ""
			response_time = ""
			if hours_and_response:
				# Try to parse with separators first
				if ' | ' in hours_and_response:
					parts = hours_and_response.split(' | ')
					for part in parts:
						part = part.strip()
						if part.lower().startswith('hours:'):
							support_hours = part[6:].strip()
						elif part.lower().startswith('response:'):
							response_time = part[9:].strip()
				elif '\n' in hours_and_response:
					lines = hours_and_response.split('\n')
					for line in lines:
						line = line.strip()
						if line.lower().startswith('hours:'):
							support_hours = line[6:].strip()
						elif line.lower().startswith('response:'):
							response_time = line[9:].strip()
						elif not support_hours:
							support_hours = line
						elif not response_time:
							response_time = line
				else:
					# If no separators, treat as support hours
					support_hours = hours_and_response

			# Add parsed values to validation
			content_to_validate["support_hours"] = support_hours
			content_to_validate["response_time"] = response_time

			# Validate and format all content with enhanced async validation
			formatted_content, validation_warnings = await validate_and_format_panel_content(content_to_validate)

			# Update ticket configuration with validated content
			updates_made = []
			warning_messages = []

			if formatted_content["welcome_message"]:
				ticket_config["welcome_message"] = formatted_content["welcome_message"]
				updates_made.append("Welcome Message")
			elif "welcome_message" in ticket_config:
				del ticket_config["welcome_message"]

			if formatted_content["welcome_image_url"]:
				ticket_config["welcome_image_url"] = formatted_content["welcome_image_url"]
				updates_made.append("Welcome Image")
				logger.info(f"Updated welcome_image_url to: {formatted_content['welcome_image_url']}")
			elif "welcome_image_url" in ticket_config:
				del ticket_config["welcome_image_url"]
				logger.info("Removed welcome_image_url from config")

			if formatted_content["support_team_name"]:
				ticket_config["support_team_name"] = formatted_content["support_team_name"]
				updates_made.append("Support Team Name")
			elif "support_team_name" in ticket_config:
				del ticket_config["support_team_name"]

			if formatted_content["custom_footer"]:
				ticket_config["custom_footer"] = formatted_content["custom_footer"]
				updates_made.append("Custom Footer")
				logger.info(f"Updated custom_footer to: {formatted_content['custom_footer']}")
			elif "custom_footer" in ticket_config:
				del ticket_config["custom_footer"]
				logger.info("Removed custom_footer from config")

			if formatted_content["support_hours"]:
				ticket_config["support_hours"] = formatted_content["support_hours"]
				updates_made.append("Support Hours")
			elif "support_hours" in ticket_config:
				del ticket_config["support_hours"]

			if formatted_content["response_time"]:
				ticket_config["response_time"] = formatted_content["response_time"]
				updates_made.append("Response Time")
			elif "response_time" in ticket_config:
				del ticket_config["response_time"]

			# Save configuration
			await save_ticket_data()

			# Create success embed with bulletproof size constraint information
			if updates_made:
				embed = discord.Embed(
					title="✅ Ticket Appearance Updated Successfully!",
					description=f"**Updated settings:** {', '.join(updates_made)}",
					color=0x00ff00  # Green for success
				)

				# Add size constraints info
				embed.add_field(
					name="📏 Bulletproof Size Constraints Applied",
					value=(
						f"• Welcome Message: {len(formatted_content.get('welcome_message', ''))}/250 chars\n"
						f"• Support Team Name: {len(formatted_content.get('support_team_name', ''))}/50 chars\n"
						f"• Custom Footer: {len(formatted_content.get('custom_footer', ''))}/100 chars"
					),
					inline=False
				)
			else:
				embed = discord.Embed(
					title="✅ Configuration Saved!",
					description="All settings have been reset to defaults.",
					color=0x00ff00  # Green for success
				)

			# Add validation warnings as a separate field if any
			if validation_warnings:
				warning_color = 0xff9900  # Orange for warnings
				embed.color = warning_color
				embed.add_field(
					name="⚠️ Validation Warnings",
					value="\n".join([f"• {warning}" for warning in validation_warnings]),
					inline=False
				)

			# Add image info if provided
			if formatted_content["welcome_image_url"]:
				recommendations = get_image_size_recommendations()
				embed.add_field(
					name="🖼️ Image Information",
					value=(
						f"Images are displayed as full-width banners with automatic scaling.\n"
						f"**Recommended:** {recommendations['aspect_ratio']} aspect ratio, "
						f"max {recommendations['max_width']}x{recommendations['max_height']}px."
					),
					inline=False
				)

			# Add tip footer
			embed.set_footer(text="💡 Tip: Use the 'Recreate Ticket Panel' button to apply these changes to your existing ticket panel.")

			await interaction.followup.send(embed=embed, ephemeral=True)

		except Exception as e:
			logger.error(f"Error in consolidated appearance modal: {e}")
			traceback.print_exc()

			# Create error embed for better user experience
			error_embed = discord.Embed(
				title="❌ Configuration Error",
				description="An error occurred while saving your settings.",
				color=0xff0000  # Red for error
			)
			error_embed.add_field(
				name="Error Details",
				value=f"```{str(e)}```",
				inline=False
			)
			error_embed.add_field(
				name="What to do",
				value=(
					"• Check that your image URL is valid and accessible\n"
					"• Ensure all text fields are within character limits\n"
					"• Try again with different values\n"
					"• Contact an administrator if the problem persists"
				),
				inline=False
			)
			error_embed.set_footer(text="Error logged for debugging purposes")

			await interaction.followup.send(embed=error_embed, ephemeral=True)


# Removed unused TicketView class that was causing interaction conflicts
# The ticket system now uses TicketPanelView with dropdowns instead of buttons

class TicketChannel(View):
	def __init__(self):
		super().__init__(timeout=None)

		# Add ticket management buttons
		close_btn = Button(label="Close", style=discord.ButtonStyle.red, custom_id="close_ticket")
		close_reason_btn = Button(label="Close With Reason", style=discord.ButtonStyle.red, custom_id="close_with_reason")

		self.add_item(close_btn)
		self.add_item(close_reason_btn)
		# Removed claim button as it's not functional




# Duplicate function removed - using the correct version below




async def load_ticket_data():
	"""Load ticket configuration and data from MongoDB"""
	global ticket_config, active_tickets

	try:
		# Use the global MongoDB connection that's already established
		# Load ticket configuration from MongoDB
		data = ticket_config_collection.find_one({"_id": "ticket_config"})
		if data:
			# Load config
			if 'config' in data:
				ticket_config.update(data['config'])

				# Ensure welcome_image_url is properly loaded
				if 'welcome_image_url' in data['config']:
					ticket_config['welcome_image_url'] = data['config']['welcome_image_url']
					print(f"Loaded welcome image URL: {ticket_config['welcome_image_url']}")

			# Load active tickets and counter
			active_tickets.update(data.get('active_tickets', {}))

			# Load last ticket number
			if 'last_ticket_number' in data:
				ticket_config['last_ticket_number'] = data['last_ticket_number']

			print(f"Loaded ticket configuration from MongoDB: {ticket_config}")
			print(f"Loaded active tickets: {active_tickets}")
			print(f"Loaded last ticket number: {ticket_config.get('last_ticket_number', 0)}")

			# Recreate panel if ticket channel exists
			if ticket_config.get("ticket_channel"):
				channel = bot.get_channel(ticket_config["ticket_channel"])
				if channel:
					await create_ticket_panel(channel)
					print("Recreated ticket panel successfully")
				else:
					print(f"Warning: Could not find ticket channel {ticket_config['ticket_channel']}")
		else:
			print("No ticket configuration found in MongoDB. Starting with empty configuration.")

		return True

	except Exception as e:
		print(f"Error loading ticket data from MongoDB: {e}")
		traceback.print_exc()
		return False

async def save_ticket_data():
	"""Save ticket configuration and data to MongoDB"""
	try:
		# Ensure all message settings are properly included
		message_settings = {
			"welcome_message": ticket_config.get("welcome_message"),
			"welcome_image_url": ticket_config.get("welcome_image_url"),
			"support_team_name": ticket_config.get("support_team_name"),
			"custom_footer": ticket_config.get("custom_footer"),
			"support_hours": ticket_config.get("support_hours", "Monday-Friday: 9AM-5PM\nWeekends: Limited Support"),
			"response_time": ticket_config.get("response_time", "Our team typically responds within 24 hours during business days.")
		}

		# Update ticket_config with message settings
		for key, value in message_settings.items():
			if value is not None:
				ticket_config[key] = value

		# Prepare data for MongoDB using the global connection
		data = {
			"_id": "ticket_config",
			"config": ticket_config,
			"active_tickets": active_tickets,
			"last_ticket_number": ticket_config.get("last_ticket_number", 0)
		}

		# Use upsert to either update existing document or insert new one
		result = ticket_config_collection.replace_one(
			{"_id": "ticket_config"},
			data,
			upsert=True
		)

		logger.info(f"Saved ticket configuration successfully to MongoDB")
		logger.debug(f"Saved message settings: {message_settings}")
		return True

	except Exception as e:
		logger.error(f"Error saving ticket data to MongoDB: {e}")
		traceback.print_exc()
		return False






class TicketCategoryButton(discord.ui.Button):
    """Individual button for each ticket category with GTA RP theming"""
    def __init__(self, category_id, category_info):
        category_name = category_info["name"]

        # GTA RP themed emoji mapping
        emoji = "🎫"  # Default
        style = discord.ButtonStyle.secondary

        # Map category names to GTA RP themed emojis and styles
        name_lower = category_name.lower()
        if "report" in name_lower and "player" in name_lower:
            emoji = "🚨"
            style = discord.ButtonStyle.danger
        elif "refund" in name_lower or "money" in name_lower:
            emoji = "💰"
            style = discord.ButtonStyle.success
        elif "bug" in name_lower:
            emoji = "🐛"
            style = discord.ButtonStyle.secondary
        elif "whitelist" in name_lower or "apply" in name_lower:
            emoji = "📋"
            style = discord.ButtonStyle.primary
        elif "help" in name_lower or "general" in name_lower:
            emoji = "❓"
            style = discord.ButtonStyle.secondary

        super().__init__(
            label=category_name,
            emoji=emoji,
            style=style,
            custom_id=f"ticket_category_btn_{category_id}"
        )
        self.category_id = category_id

class TicketCategoryDropdown(discord.ui.Select):
    """Dropdown menu for selecting ticket categories (legacy fallback)"""
    def __init__(self, categories):
        # Create options for each category
        options = []
        for category_id, category_info in categories.items():
            category_name = category_info["name"]
            description = category_info.get("description", "")

            # Choose emoji based on category name
            emoji = "🎫"  # Default ticket emoji
            if "support" in category_name.lower():
                emoji = "❓"
            elif "bug" in category_name.lower():
                emoji = "🐛"
            elif "feature" in category_name.lower():
                emoji = "✨"
            elif "general" in category_name.lower():
                emoji = "💬"

            # Truncate description if too long
            if len(description) > 100:
                description = description[:97] + "..."

            options.append(discord.SelectOption(
                label=category_name,
                description=description or "Click to create a ticket",
                value=str(category_id),
                emoji=emoji
            ))

        super().__init__(
            placeholder="🎫 Select a category to create a ticket...",
            min_values=1,
            max_values=1,
            options=options,
            custom_id="ticket_category_dropdown_v2"  # Changed to v2 to avoid conflicts
        )

    # Callback removed - handled by global interaction handler in bot.py
    # This prevents duplicate interaction handling and conflicts

class GtaRpTicketPanelView(discord.ui.View):
    """GTA RP themed view with the 5 specific interactive buttons"""
    def __init__(self, categories=None):
        super().__init__(timeout=None)

        # Define the 5 specific categories with GTA RP theming
        self.predefined_categories = [
            {"name": "🚨 Report a Player", "emoji": "🚨", "style": discord.ButtonStyle.danger, "row": 0},
            {"name": "💰 Refund Request", "emoji": "💰", "style": discord.ButtonStyle.success, "row": 0},
            {"name": "🐛 Bug Report", "emoji": "🐛", "style": discord.ButtonStyle.secondary, "row": 1},
            {"name": "📋 Apply for Whitelist", "emoji": "📋", "style": discord.ButtonStyle.primary, "row": 1},
            {"name": "❓ General Help", "emoji": "❓", "style": discord.ButtonStyle.secondary, "row": 2}
        ]

        # Add predefined buttons or use existing categories
        if categories and len(categories) > 0:
            self._add_category_buttons(categories)
        else:
            self._add_predefined_buttons()

    def _add_predefined_buttons(self):
        """Add the 5 predefined GTA RP themed buttons"""
        for i, category in enumerate(self.predefined_categories):
            button = discord.ui.Button(
                label=category["name"],
                emoji=category["emoji"],
                style=category["style"],
                custom_id=f"ticket_category_predefined_{i + 1}",
                row=category["row"]
            )
            self.add_item(button)

    def _add_category_buttons(self, categories):
        """Add buttons based on existing categories with GTA RP styling"""
        row = 0
        col = 0

        for category_id, category_info in categories.items():
            category_name = category_info["name"]

            # Choose emoji and style based on category name
            emoji = "🎫"  # Default
            style = discord.ButtonStyle.secondary

            # Map category names to GTA RP themed emojis and styles
            name_lower = category_name.lower()
            if "report" in name_lower or "player" in name_lower:
                emoji = "🚨"
                style = discord.ButtonStyle.danger
            elif "refund" in name_lower or "money" in name_lower or "cash" in name_lower:
                emoji = "💰"
                style = discord.ButtonStyle.success
            elif "bug" in name_lower:
                emoji = "🐛"
                style = discord.ButtonStyle.secondary
            elif "whitelist" in name_lower or "apply" in name_lower or "application" in name_lower:
                emoji = "📋"
                style = discord.ButtonStyle.primary
            elif "help" in name_lower or "support" in name_lower or "general" in name_lower:
                emoji = "❓"
                style = discord.ButtonStyle.secondary

            button = discord.ui.Button(
                label=category_name,
                emoji=emoji,
                style=style,
                custom_id=f"ticket_category_{category_id}",
                row=row
            )

            self.add_item(button)

            col += 1
            if col >= 5:  # Discord max buttons per row
                col = 0
                row += 1
                if row >= 5:  # Discord max rows
                    break

class TicketPanelView(discord.ui.View):
    """Legacy view containing the ticket category dropdown (fallback)"""
    def __init__(self, categories):
        super().__init__(timeout=None)

        # Only add dropdown if there are categories
        if categories:
            self.add_item(TicketCategoryDropdown(categories))

async def clear_old_ticket_views():
    """Clear any old persistent views to prevent conflicts"""
    try:
        # This function helps prevent interaction conflicts by ensuring
        # we start with a clean slate for persistent views
        logger.info("Clearing old ticket views to prevent interaction conflicts")

        # Note: Discord.py automatically handles view cleanup when views timeout
        # or when new views with the same custom_id are created

    except Exception as e:
        logger.error(f"Error clearing old views: {e}")

async def create_ticket_panel(channel):
    """Create a GTA RP themed ticket panel with immersive Los Santos aesthetics and live statistics"""
    try:
        # Clear any old views first to prevent conflicts
        await clear_old_ticket_views()

        # Log panel creation
        logger.info(f"Creating GTA RP ticket panel in channel {channel.name} (ID: {channel.id})")

        # Get live statistics for the footer
        stats = await get_ticket_statistics(channel.guild.id)

        # GTA RP themed content with Los Santos flavor
        content_to_validate = {
            "welcome_message": ticket_config.get("welcome_message", ""),
            "welcome_image_url": ticket_config.get("welcome_image_url", ""),
            "support_team_name": "Crimson Team",  # Fixed GTA RP team name
            "custom_footer": ticket_config.get("custom_footer", ""),
            "support_hours": ticket_config.get("support_hours", "🕒 Mon–Fri: 9AM–5PM PST\n🕒 Sat–Sun: Limited Availability"),
            "response_time": ticket_config.get("response_time", "⏱️ Response Time: Within 24 hours")
        }

        # Apply bulletproof validation and formatting
        formatted_content, validation_warnings = await validate_and_format_panel_content(content_to_validate)

        # Log any validation warnings and store them for potential user notification
        if validation_warnings:
            logger.info(f"Panel content validation warnings: {validation_warnings}")
            # Store warnings in ticket_config for potential display to admins
            ticket_config["_last_validation_warnings"] = validation_warnings

        # Get validated content with GTA RP theming
        support_team_name = "Crimson Team"  # Fixed GTA RP branding

        # GTA RP themed welcome message with urban support interface styling
        welcome_message = formatted_content.get("welcome_message") or (
            "⚠️ **Got a problem on the streets of Los Santos?** Whether it's a stolen ride, "
            "broken system, or corrupt cop, our **Crimson Team** is on patrol to back you up."
        )

        welcome_image_url = formatted_content.get("welcome_image_url")
        support_hours = formatted_content.get("support_hours") or "🕒 Mon–Fri: 9AM–5PM PST\n🕒 Sat–Sun: Limited Availability"
        response_time = formatted_content.get("response_time") or "⏱️ Response Time: Within 24 hours"
        custom_footer = formatted_content.get("custom_footer")

        # Get categories for button creation
        categories = ticket_config.get("categories", {})
        sorted_categories = sorted(
            categories.items(),
            key=lambda x: x[1].get("name", "").lower()
        ) if categories else []

        # Create GTA RP themed embed with wide, immersive design and fixed dimensions
        embed = discord.Embed(
            title="🌃 **Los Santos Support Terminal**",
            description=content_validator.truncate_text(
                welcome_message,
                content_validator.MAX_DESCRIPTION_LENGTH
            ),
            color=0xDC143C  # Crimson red color (#DC143C)
        )

        # Smart image handling with dedicated space for RP-themed visuals
        if welcome_image_url:
            # Use set_image for full-width banner (maintains consistent layout)
            embed.set_image(url=welcome_image_url)
            # Add thumbnail for additional visual space if guild has icon
            if channel.guild.icon:
                embed.set_thumbnail(url=channel.guild.icon.url)
        else:
            # Fallback behavior when no image provided
            if channel.guild.icon:
                embed.set_thumbnail(url=channel.guild.icon.url)
            # Add a placeholder field to maintain consistent embed height
            embed.add_field(
                name="🏙️ **Los Santos Police Department**",
                value="*Visual space reserved for RP content*",
                inline=False
            )

        # Support Information Section with GTA RP theming
        support_info = f"🕒 **Support Hours**\n{support_hours}\n\n{response_time}"
        embed.add_field(
            name="📋 **Support Information**",
            value=content_validator.truncate_text(support_info, 400),
            inline=False
        )

        # Live Statistics Footer with dynamic data
        stats_text = (
            f"✅ **Tickets Closed This Week:** {stats['closed_this_week']}\n"
            f"📁 **Active Tickets:** {stats['active_tickets']}\n"
            f"⏳ **Avg. Resolution:** {stats['avg_resolution_hours']} hours"
        )
        embed.add_field(
            name="📊 **Live Statistics**",
            value=stats_text,
            inline=False
        )

        # Footer with consistent branding and timestamp
        current_time = datetime.now().strftime("%m/%d/%Y %I:%M %p")
        footer_text = custom_footer or f"Crimson Team Support • {current_time}"
        embed.set_footer(text=content_validator.truncate_text(footer_text, content_validator.MAX_FOOTER_LENGTH))

        # Create view with interactive buttons (replaces dropdown)
        view = GtaRpTicketPanelView(categories)

        # Clear existing messages
        messages_to_delete = []
        try:
            async for message in channel.history(limit=10):
                if message.author == bot.user:
                    messages_to_delete.append(message)

            if len(messages_to_delete) > 1 and hasattr(channel, 'delete_messages'):
                try:
                    await channel.delete_messages(messages_to_delete)
                    logger.info(f"Bulk deleted {len(messages_to_delete)} messages from ticket panel channel")
                except discord.HTTPException:
                    for message in messages_to_delete:
                        await message.delete()
            else:
                for message in messages_to_delete:
                    await message.delete()
        except Exception as e:
            logger.warning(f"Error clearing messages: {e}")

        # Send the new panel
        panel_message = await channel.send(embed=embed, view=view)

        # Store the panel message ID
        if panel_message:
            ticket_config["panel_message_id"] = panel_message.id
            asyncio.create_task(save_ticket_data())
            logger.info(f"Ticket panel created successfully with message ID: {panel_message.id}")

        return True

    except Exception as e:
        logger.error(f"Error creating ticket panel: {e}")
        traceback.print_exc()
        return False








async def create_application_ticket(interaction: discord.Interaction, category_id: int, applicant_user, application_name: str):
	"""Create a new ticket for an application with the applicant automatically added - matches main ticket system styling"""
	try:
		# Get the next ticket number
		ticket_config["last_ticket_number"] = ticket_config.get("last_ticket_number", 0) + 1
		ticket_number = ticket_config["last_ticket_number"]

		# Get guild and category
		guild = interaction.guild
		category = guild.get_channel(category_id)

		if not category:
			logger.error(f"Category {category_id} not found")
			return False

		# Create the ticket channel
		channel_name = f"ticket-{ticket_number:04d}"
		overwrites = {
			guild.default_role: discord.PermissionOverwrite(read_messages=False),
			applicant_user: discord.PermissionOverwrite(read_messages=True, send_messages=True),
			interaction.user: discord.PermissionOverwrite(read_messages=True, send_messages=True)  # Staff member who created it
		}

		# Add staff roles to overwrites
		for role_id in ticket_config.get("staff_roles", []):
			role = guild.get_role(role_id)
			if role:
				overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

		# Create the channel
		channel = await guild.create_text_channel(
			channel_name,
			category=category,
			overwrites=overwrites,
			topic=f"Application ticket for {applicant_user.display_name} - {application_name}"
		)

		# Store ticket data
		active_tickets[str(channel.id)] = {
			"user_id": applicant_user.id,
			"ticket_number": ticket_number,
			"claimed_by": None,
			"application_related": True,
			"application_name": application_name,
			"created_by_staff": interaction.user.id
		}

		# Save ticket data
		await save_ticket_data()

		# Get configuration values matching main ticket system
		support_team_name = ticket_config.get("support_team_name", "Support Team")
		support_hours = ticket_config.get("support_hours", "Monday-Friday: 9AM-5PM\nWeekends: Limited Support")
		response_time = ticket_config.get("response_time", "Our team typically responds within 24 hours during business days.")

		# Send a separate message with the user mention to properly ping them (matching main system)
		mention_message = await channel.send(f"{applicant_user.mention}")

		# Create a single consolidated professional ticket embed matching main system exactly
		ticket_embed = discord.Embed(
			title=f"{support_team_name} Ticket",
			description=(
				f"Thank you for contacting our {support_team_name}.\n\n"
				f"A member of our team will assist you shortly. Please provide any additional information that may help us address your request efficiently."
			),
			color=0x000000  # Pure black color for professional look - matching main system
		)

		# Add ticket ID with proper formatting (matching main system)
		ticket_embed.add_field(
			name="**Ticket ID**",
			value=f"#{ticket_number:04d}",
			inline=False
		)

		# Add elegant separator for visual hierarchy (matching main system)
		ticket_embed.add_field(
			name="\u200b",  # Invisible field name for spacing
			value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
			inline=False
		)

		# Add support hours information with consistent formatting (matching main system)
		ticket_embed.add_field(
			name="**Support Hours**",
			value=support_hours,
			inline=True
		)

		ticket_embed.add_field(
			name="**Expected Response Time**",
			value=response_time,
			inline=True
		)

		# Add elegant separator before application details (matching main system)
		ticket_embed.add_field(
			name="\u200b",  # Invisible field name for spacing
			value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
			inline=False
		)

		# Add Application Details section header with improved formatting (matching main system style)
		ticket_embed.add_field(
			name="**Application Details**",
			value="This ticket was created to follow up on the application submission below:",
			inline=False
		)

		# Add application context with dark box formatting (matching main system)
		ticket_embed.add_field(
			name="**Application Type**",
			value=f"```{application_name}```",
			inline=False
		)

		ticket_embed.add_field(
			name="**Applicant**",
			value=f"```{applicant_user.display_name} (@{applicant_user.name})```",
			inline=False
		)

		ticket_embed.add_field(
			name="**Created by Staff**",
			value=f"```{interaction.user.display_name} (@{interaction.user.name})```",
			inline=False
		)

		# Add elegant separator at the bottom (matching main system)
		ticket_embed.add_field(
			name="\u200b",  # Invisible field name for spacing
			value="━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━",
			inline=False
		)

		# Add custom footer with improved formatting (matching main system)
		current_time = datetime.now().strftime("%d %b %Y • %I:%M %p")
		footer_text = ticket_config.get("custom_footer", f"Ticket #{ticket_number:04d} • Created {current_time}")
		ticket_embed.set_footer(text=footer_text)

		# Create a view with buttons for ticket actions (matching main system exactly)
		view = discord.ui.View(timeout=None)  # Persistent view

		# Create buttons for ticket actions with consistent styling (matching main system)
		close_button = discord.ui.Button(
			label="Close Ticket",
			style=discord.ButtonStyle.secondary,
			custom_id="close_ticket",
			emoji="🔒",
			row=0
		)

		close_reason_button = discord.ui.Button(
			label="Close with Reason",
			style=discord.ButtonStyle.red,
			custom_id="close_with_reason",
			emoji="📝",
			row=0
		)

		# Add buttons to the view (matching main system)
		view.add_item(close_button)
		view.add_item(close_reason_button)

		# Send the single consolidated ticket embed with buttons (matching main system)
		ticket_message = await channel.send(embed=ticket_embed, view=view)

		# Store message IDs including the mention message (matching main system)
		message_ids = [mention_message.id, ticket_message.id]

		# Update ticket info with message IDs (matching main system structure)
		active_tickets[str(channel.id)].update({
			"mention_message_id": mention_message.id,  # User mention message
			"ticket_message_id": ticket_message.id,  # Main ticket message with consolidated embed and buttons
			"message_ids": message_ids  # All message IDs
		})

		await save_ticket_data()

		logger.info(f"Application ticket created: {channel.name} (ID: {channel.id}) for user {applicant_user.id}")
		return True

	except Exception as e:
		logger.error(f"Error creating application ticket: {e}")
		traceback.print_exc()
		return False

async def create_ticket(interaction: discord.Interaction, category_id: int):
	"""Create a new ticket with bulletproof interaction handling"""
	# Flag to track if we've responded to the interaction
	interaction_responded = False

	try:
		# Check if user is rate limited
		user_id = interaction.user.id
		rate_limit_key = f"ticket_create:{user_id}"

		# Simple rate limiting to prevent spam
		current_time = time.time()
		if rate_limit_key in ticket_rate_limiter.rate_limits:
			wait_time = ticket_rate_limiter.rate_limits[rate_limit_key] - current_time
			if wait_time > 0:
				# User is rate limited - send simple embed notification
				if not interaction.response.is_done():
					rate_limit_embed = discord.Embed(
						title="⏱️ Slow Down",
						description=f"Please wait **{wait_time:.0f} seconds** before creating another ticket.",
						color=0xff9900  # Orange for warning
					)
					rate_limit_embed.set_footer(text=get_dynamic_footer_text(interaction.guild))
					await interaction.response.send_message(embed=rate_limit_embed, ephemeral=True)
					interaction_responded = True
				return

		# Set rate limit (one ticket per 30 seconds per user)
		ticket_rate_limiter.rate_limits[rate_limit_key] = current_time + 30

		# Convert category_id to string for comparison since JSON stores keys as strings
		category_id_str = str(category_id)

		# Verify category exists
		if category_id_str not in ticket_config.get("categories", {}):
			if not interaction.response.is_done():
				await interaction.response.send_message(
					"This ticket category no longer exists. Please contact an administrator.",
					ephemeral=True
				)
				interaction_responded = True
			return

		# Check concurrent ticket limit before proceeding
		user_open_tickets = count_user_open_tickets(user_id)
		max_tickets = ticket_config.get("max_tickets_per_user", 3)

		if user_open_tickets >= max_tickets:
			# User has reached concurrent ticket limit
			if not interaction.response.is_done():
				limit_embed = discord.Embed(
					title="🚫 Ticket Limit Reached",
					description=f"You have reached the maximum of **{max_tickets}** open tickets.",
					color=0xff0000  # Red for error
				)

				limit_embed.add_field(
					name="Current Open Tickets",
					value=f"{user_open_tickets}/{max_tickets}",
					inline=True
				)

				limit_embed.add_field(
					name="What can you do?",
					value=(
						"• Close an existing ticket to create a new one\n"
						"• Use your existing tickets for additional questions\n"
						"• Contact staff directly if this is urgent"
					),
					inline=False
				)

				limit_embed.set_footer(text=get_dynamic_footer_text(interaction.guild))
				await interaction.response.send_message(embed=limit_embed, ephemeral=True)
				interaction_responded = True
			return

		# Get the next ticket number (but don't increment yet - only increment on successful creation)
		next_ticket_number = ticket_config.get("last_ticket_number", 0) + 1

		# Create and send modal with the prospective ticket number
		modal = TicketModal(category_id, next_ticket_number)

		# Send modal - this is our final response
		if not interaction.response.is_done():
			await interaction.response.send_modal(modal)
			interaction_responded = True
		else:
			logger.warning("Interaction already responded to when trying to send modal")

	except discord.NotFound:
		logger.error("Interaction not found or expired")
	except discord.HTTPException as e:
		if e.code == 40060:  # Interaction already acknowledged
			logger.warning("Interaction already acknowledged - this is expected in some cases")
		else:
			logger.error(f"Discord HTTP error creating ticket: {e}")
	except Exception as e:
		logger.error(f"Error creating ticket: {e}")
		traceback.print_exc()

		# Only try to send error message if we haven't responded yet
		if not interaction_responded and not interaction.response.is_done():
			try:
				await interaction.response.send_message(
					"An error occurred while creating the ticket. Please try again later.",
					ephemeral=True
				)
			except Exception as follow_up_error:
				logger.error(f"Could not send error message: {follow_up_error}")

@bot.command(name="recreate_ticket_panel", description="Recreate the ticket panel with black theme")
@commands.has_permissions(administrator=True)
async def recreate_ticket_panel(ctx):
    """Recreate the ticket panel with black theme"""
    try:
        # Check if ticket channel is set
        if not ticket_config.get("ticket_channel"):
            await ctx.send("No ticket channel is configured. Please set a ticket channel first.")
            return

        # Get the channel
        channel = ctx.guild.get_channel(ticket_config["ticket_channel"])
        if not channel:
            await ctx.send("The configured ticket channel no longer exists. Please set a new one.")
            return

        # Create the ticket panel
        success = await create_ticket_panel(channel)

        if success:
            await ctx.send(f"Ticket panel recreated successfully in {channel.mention}.")
        else:
            await ctx.send("There was an error recreating the ticket panel. Please check the logs.")
    except Exception as e:
        logger.error(f"Error recreating ticket panel: {e}")
        traceback.print_exc()
        await ctx.send(f"Error: {str(e)}")

@bot.command(name="debug_ticket_image", description="Debug the ticket welcome image")
@commands.has_permissions(administrator=True)
async def debug_ticket_image(ctx):
	"""Debug command to check the current welcome image URL"""
	try:
		welcome_image_url = ticket_config.get("welcome_image_url")

		embed = discord.Embed(
			title="Ticket Image Debug",
			description="Current welcome image configuration",
			color=0x000000  # Pure black color for consistency
		)

		if welcome_image_url:
			embed.add_field(
				name="Welcome Image URL",
				value=welcome_image_url,
				inline=False
			)
			embed.set_image(url=welcome_image_url)
		else:
			embed.add_field(
				name="Welcome Image URL",
				value="No custom welcome image URL set",
				inline=False
			)
			# Show default image
			default_image = "https://cdn.discordapp.com/attachments/1321118061520224287/1366986791923617925/support-agent.png"
			embed.set_image(url=default_image)
			embed.add_field(
				name="Default Image",
				value=default_image,
				inline=False
			)

		# Add MongoDB data
		try:
			client = pymongo.MongoClient("mongodb://localhost:27017/")
			db = client["missminutesbot"]
			ticket_collection = db["ticket_config"]

			data = ticket_collection.find_one({"_id": "ticket_config"})
			if data and 'config' in data:
				mongo_image_url = data['config'].get('welcome_image_url', 'Not found in MongoDB')
				embed.add_field(
					name="MongoDB Image URL",
					value=mongo_image_url,
					inline=False
				)
		except Exception as e:
			embed.add_field(
				name="MongoDB Error",
				value=str(e),
				inline=False
			)

		await ctx.send(embed=embed)
	except Exception as e:
		logger.error(f"Error in debug_ticket_image: {e}")
		traceback.print_exc()
		await ctx.send(f"Error: {str(e)}")



def get_ticket_commands():
	return [setup_ticket_system]

async def claim_ticket(channel_id: int, staff_member: discord.Member):
	"""Claim a ticket"""
	try:
		if str(channel_id) not in active_tickets:
			return False, "This is not a ticket channel"

		ticket_data = active_tickets[str(channel_id)]

		# Check if ticket is already claimed
		if ticket_data.get('claimed_by'):
			return False, "Ticket is already claimed"

		channel = bot.get_channel(channel_id)
		if not channel:
			return False, "Channel not found"

		# Update ticket data
		ticket_data['claimed_by'] = staff_member.id
		await save_ticket_data()

		# Create claim embed
		embed = discord.Embed(
			title="Ticket Claimed",
			description=f"This ticket has been claimed by {staff_member.mention}",
			color=0x000000  # Pure black color for consistency
		)

		# Send claim notification
		await channel.send(embed=embed)

		# Update original ticket message if it exists
		if 'message_id' in ticket_data:
			try:
				message = await channel.fetch_message(ticket_data['message_id'])
				if message:
					original_embed = message.embeds[0]
					original_embed.add_field(
						name="Claimed By",
						value=staff_member.mention,
						inline=False
					)
					await message.edit(embed=original_embed)
			except:
				pass  # Original message might be deleted or inaccessible

		return True, None

	except Exception as e:
		print(f"Error claiming ticket: {e}")
		traceback.print_exc()
		return False, str(e)

async def reopen_ticket(channel_id: int, user):
	"""Reopen a closed ticket with optimized flow"""
	try:
		channel = user.guild.get_channel(channel_id)
		if not channel:
			return False, "Channel not found"

		if not channel.name.startswith('closed-'):
			return False, "This ticket is not closed"

		# Extract ticket number and prepare new name
		ticket_number = channel.name.split('-')[1]
		new_name = f"ticket-{ticket_number}"

		# Update channel name first
		await asyncio.sleep(1)  # Small initial delay
		await channel.edit(name=new_name)
		await asyncio.sleep(2)  # Wait between operations

		# Update permissions
		overwrites = {
			channel.guild.default_role: discord.PermissionOverwrite(read_messages=False),
			user.guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
		}

		for role_id in ticket_config.get("staff_roles", []):
			role = channel.guild.get_role(role_id)
			if role:
				overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

		await channel.edit(overwrites=overwrites)
		await asyncio.sleep(2)

		# Create single embed with all controls
		embed = discord.Embed(
			description=f"Ticket Controls\nOpened by {user.mention}",
			color=discord.Color.green()
		)

		view = View()
		close_btn = Button(
			label="Close",
			style=discord.ButtonStyle.red,
			custom_id="close_ticket",
			emoji="🔒"
		)
		delete_btn = Button(
			label="Delete",
			style=discord.ButtonStyle.red,
			custom_id="delete_ticket",
			emoji="🗑️"
		)
		transcript_btn = Button(
			label="Transcript",
			style=discord.ButtonStyle.blurple,
			custom_id="view_transcript",
			emoji="📜"
		)

		view.add_item(close_btn)
		view.add_item(delete_btn)
		view.add_item(transcript_btn)

		await channel.send(embed=embed, view=view)

		# Update ticket status
		channel_id_str = str(channel_id)
		active_tickets[channel_id_str] = {
			"status": "open",
			"reopened_by": user.id,
			"reopened_at": datetime.now().isoformat()
		}
		await save_ticket_data()

		return True, None

	except Exception as e:
		print(f"Error reopening ticket: {e}")
		return False, str(e)






class TicketOperationQueue:
	def __init__(self):
		self.queue = asyncio.Queue()
		self._running = False
		self.processing_lock = asyncio.Lock()
		self.operation_delays = {
			'rename': 2.0,
			'permissions': 1.0,
			'message': 0.5
		}

	async def add_operation(self, operation_type, func, *args, **kwargs):
		await self.queue.put((operation_type, func, args, kwargs))
		if not self._running:
			self._running = True
			asyncio.create_task(self._process_queue())

	async def _process_queue(self):
		while True:
			try:
				if self.queue.empty():
					self._running = False
					break

				async with self.processing_lock:
					op_type, func, args, kwargs = await self.queue.get()
					try:
						await func(*args, **kwargs)
						await asyncio.sleep(self.operation_delays.get(op_type, 1.0))
					except AttributeError as e:
						if "'str' object has no attribute 'to_dict'" in str(e):
							# Skip this error - it's a known issue with Discord object serialization
							logger.debug(f"Skipping serialization error in operation {op_type}: {e}")
						else:
							logger.error(f"AttributeError in operation {op_type}: {e}")
					except Exception as e:
						logger.error(f"Error in operation {op_type}: {e}")
					finally:
						self.queue.task_done()
			except Exception as e:
				print(f"Queue processing error: {e}")
				await asyncio.sleep(1)




async def close_ticket(channel_id: int, reason: str = None, closer: discord.Member = None):
    """Close a ticket with enhanced handling for high performance and professional appearance"""
    try:
        # Get channel with error handling
        channel = closer.guild.get_channel(channel_id)
        if not channel:
            return False, "Channel not found"

        # Check if channel is already closed
        if channel.name.startswith('closed-'):
            return False, "Ticket is already closed"

        # Start a task to collect messages while we do other operations
        message_collection_task = asyncio.create_task(collect_ticket_messages(channel))

        # Update channel name with rate limiting - do this first for visual feedback
        ticket_number = channel.name.split('-', 1)[1] if '-' in channel.name else "unknown"
        new_name = f"closed-{ticket_number}"

        try:
            await ticket_rate_limiter.execute(
                'edit_channel',
                channel.edit,
                name=new_name
            )
        except Exception as e:
            logger.warning(f"Error updating channel name: {e}")
            # Continue anyway - this is not critical

        # Send initial closure message to provide immediate feedback
        initial_close_embed = discord.Embed(
            title="Ticket Closing",
            description="This ticket is being closed and archived. Please wait...",
            color=0x2b2d31
        )
        initial_close_embed.set_footer(text="Preparing transcript...")

        initial_message = await channel.send(embed=initial_close_embed)

        # Update permissions with rate limiting
        overwrites = {
            channel.guild.default_role: discord.PermissionOverwrite(read_messages=False),
            closer.guild.me: discord.PermissionOverwrite(read_messages=True, send_messages=True)
        }

        # Add staff roles
        for role_id in ticket_config.get("staff_roles", []):
            role = channel.guild.get_role(role_id)
            if role:
                overwrites[role] = discord.PermissionOverwrite(read_messages=True, send_messages=True)

        # Apply permission changes
        try:
            await ticket_rate_limiter.execute(
                'edit_channel',
                channel.edit,
                overwrites=overwrites
            )
        except Exception as e:
            logger.warning(f"Error updating channel permissions: {e}")
            # Continue anyway - this is not critical

        # Wait for message collection to complete
        try:
            messages = await asyncio.wait_for(message_collection_task, timeout=30.0)
        except asyncio.TimeoutError:
            logger.warning(f"Message collection timed out for ticket {ticket_number}")
            messages = []  # Use empty list if timed out
        except Exception as e:
            logger.error(f"Error collecting messages: {e}")
            messages = []

        # Prepare transcript data with enhanced metadata
        close_time = datetime.now()

        # Try to find when the ticket was created
        created_at = None
        created_by = None
        first_message = None

        if messages and len(messages) > 0:
            # Try to find the first message
            first_message = messages[0]
            created_at = first_message.get("timestamp")
            created_by = first_message.get("author_id")

        # If we couldn't determine creation time from messages, use channel creation time
        if not created_at and hasattr(channel, 'created_at'):
            created_at = channel.created_at.isoformat()

        # Calculate resolution time if possible (ensure timezone consistency)
        resolution_time = None
        if created_at:
            try:
                created_dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                # Ensure close_time is timezone-aware
                if close_time.tzinfo is None:
                    close_time = close_time.replace(tzinfo=timezone.utc)
                resolution_time = (close_time - created_dt).total_seconds() / 60  # in minutes
            except Exception as e:
                logger.error(f"Error calculating resolution time: {e}")
                pass

        # Extract all participants
        participants = set()
        staff_participants = set()

        for msg in messages:
            author_id = msg.get("author_id")
            if author_id:
                participants.add(author_id)

                # Check if author is staff
                author_roles = msg.get("author_roles", [])
                for role_id in ticket_config.get("staff_roles", []):
                    if role_id in author_roles:
                        staff_participants.add(author_id)
                        break

        # Get category information
        category_id = None
        category_name = "Support"
        if channel.category and channel.category.id:
            category_id = channel.category.id
            category_id_str = str(category_id)
            if category_id_str in ticket_config.get("categories", {}):
                category_name = ticket_config["categories"][category_id_str].get("name", "Support")

        # Prepare enhanced transcript data
        transcript_data = {
            "ticket_id": ticket_number,
            "guild_id": channel.guild.id,
            "channel_id": channel.id,
            "category": category_name,
            "category_id": category_id,
            "created_by": created_by,
            "created_at": created_at,
            "closed_by": closer.id,
            "closed_by_name": closer.name,
            "close_reason": reason,
            "closed_at": close_time.isoformat(),
            "resolution_time": resolution_time,
            "message_count": len(messages),
            "participants": list(participants),
            "staff_participants": list(staff_participants),
            "staff_roles": ticket_config.get("staff_roles", []),
            "messages": messages
        }

        # Store transcript in MongoDB asynchronously
        store_task = asyncio.create_task(store_transcript_data(transcript_data))

        # Create professional closure message with buttons
        close_embed = discord.Embed(
            title="🔒 Ticket Closed",
            description=f"This support ticket has been closed by staff and archived.",
            color=0x000000,  # Professional black theme
            timestamp=close_time
        )

        # Add ticket information
        close_embed.add_field(
            name="Ticket Information",
            value=f"**ID:** {ticket_number}\n**Category:** {category_name}",
            inline=True
        )

        # Add closure information
        close_embed.add_field(
            name="Closed By",
            value=f"{closer.mention}\n{closer.name}",
            inline=True
        )

        # Add reason if provided (staff-only feature)
        if reason:
            close_embed.add_field(
                name="Closure Reason",
                value=f"📝 {reason}",
                inline=False
            )

        # Add resolution time if available
        if resolution_time:
            hours = int(resolution_time // 60)
            minutes = int(resolution_time % 60)
            close_embed.add_field(
                name="Resolution Time",
                value=f"{hours}h {minutes}m",
                inline=True
            )

        # Add message count
        close_embed.add_field(
            name="Messages",
            value=str(len(messages)),
            inline=True
        )

        # Add footer
        close_embed.set_footer(text=f"Ticket #{ticket_number} • Transcript saved")

        # Create view with professional buttons
        view = View()

        # Add Reopen button
        reopen_btn = Button(
            label="Reopen Ticket",
            style=discord.ButtonStyle.green,
            custom_id="reopen_ticket",
            emoji="🔓"
        )

        # Add Delete button
        delete_btn = Button(
            label="Delete Ticket",
            style=discord.ButtonStyle.red,
            custom_id="delete_ticket",
            emoji="🗑️"
        )

        # Add Transcript button (staff only - sends transcript in channel)
        transcript_btn = Button(
            label="Transcript",
            style=discord.ButtonStyle.blurple,
            custom_id="view_transcript_channel",
            emoji="📜"
        )

        view.add_item(reopen_btn)
        view.add_item(delete_btn)
        view.add_item(transcript_btn)

        # Send final closure message
        await channel.send(embed=close_embed, view=view)

        # Try to edit the initial message to avoid clutter
        try:
            await initial_message.delete()
        except:
            pass  # Ignore if we can't delete it

        # Wait for transcript storage to complete first to get the transcript ID
        try:
            transcript_id = await asyncio.wait_for(store_task, timeout=10.0)
            logger.info(f"Stored transcript {transcript_id} for ticket {ticket_number}")

            # Add the transcript ID to the transcript data
            transcript_data["_id"] = transcript_id

            # Now send transcript to transcript channel if configured (with proper ID)
            if ticket_config.get("transcript_channel"):
                await send_transcript_to_channel(
                    closer.guild,
                    transcript_data,
                    ticket_config["transcript_channel"]
                )

        except asyncio.TimeoutError:
            logger.warning(f"Transcript storage timed out for ticket {ticket_number}")
            # Still send transcript to channel even if storage timed out, but without ID
            if ticket_config.get("transcript_channel"):
                await send_transcript_to_channel(
                    closer.guild,
                    transcript_data,
                    ticket_config["transcript_channel"]
                )
        except Exception as e:
            logger.error(f"Error storing transcript: {e}")
            # Still send transcript to channel even if storage failed, but without ID
            if ticket_config.get("transcript_channel"):
                await send_transcript_to_channel(
                    closer.guild,
                    transcript_data,
                    ticket_config["transcript_channel"]
                )

        # Update ticket status in memory and database
        channel_id_str = str(channel_id)
        active_tickets[channel_id_str] = {
            "status": "closed",
            "closed_by": closer.id,
            "closed_at": close_time.isoformat(),
            "reason": reason,
            "transcript_id": transcript_id if 'transcript_id' in locals() else None
        }

        # Save ticket data asynchronously
        asyncio.create_task(save_ticket_data())

        return True, None

    except Exception as e:
        logger.error(f"Error closing ticket: {e}")
        traceback.print_exc()
        return False, str(e)

async def collect_ticket_messages(channel):
    """Collect all messages from a ticket channel with enhanced metadata"""
    messages = []
    try:
        async for message in channel.history(limit=None, oldest_first=True):
            # Skip system messages
            if message.type != discord.MessageType.default and message.type != discord.MessageType.reply:
                continue

            # Extract attachments
            attachments = []
            for att in message.attachments:
                attachments.append(att.url)

            # Extract embeds
            embeds_content = []
            for embed in message.embeds:
                if embed.description:
                    embeds_content.append(embed.description)

            # Extract author roles if available
            author_roles = []
            if hasattr(message.author, 'roles'):
                author_roles = [role.id for role in message.author.roles]

            # Create message data with Discord username only (no display name fallbacks)
            # Enhanced reply information capture
            reply_info = None
            if message.reference and message.reference.message_id:
                reply_info = message.reference.message_id

            msg_data = {
                "message_id": message.id,
                "timestamp": message.created_at.isoformat(),
                "author_id": message.author.id,
                "author_name": message.author.name if message.author.name else "Unknown",
                "author_roles": author_roles,
                "content": message.content,
                "attachments": attachments,
                "embeds": embeds_content,
                "is_reply": message.reference is not None,
                "reply_to": reply_info
            }

            messages.append(msg_data)

    except Exception as e:
        logger.error(f"Error collecting ticket messages: {e}")

    return messages

async def store_transcript_data(transcript_data):
    """Store transcript data in MongoDB with optimized handling"""
    try:
        # Use the transcript manager for better performance
        from transcript_manager import transcript_manager

        # Store with enhanced manager
        transcript_id = await transcript_manager.store_transcript(transcript_data, transcript_data.get("messages", []))
        return transcript_id

    except ImportError:
        # Fall back to direct MongoDB storage if transcript manager not available
        try:
            result = transcript_collection.insert_one(transcript_data)
            return str(result.inserted_id)
        except Exception as e:
            logger.error(f"Error storing transcript directly: {e}")
            return None
    except Exception as e:
        logger.error(f"Error storing transcript: {e}")
        return None

async def send_transcript_to_channel(guild, transcript_data, transcript_channel_id):
    """Send a professional transcript notification to the transcript channel with enhanced formatting"""
    try:
        transcript_channel = guild.get_channel(transcript_channel_id)
        if not transcript_channel:
            logger.warning(f"Transcript channel {transcript_channel_id} not found")
            return False

        # Get basic ticket info
        ticket_id = transcript_data.get("ticket_id", "unknown")
        category = transcript_data.get("category", "Support")
        closed_by_id = transcript_data.get("closed_by")
        closed_by_name = transcript_data.get("closed_by_name", "Unknown")
        close_reason = transcript_data.get("close_reason", "No reason provided")
        message_count = transcript_data.get("message_count", 0)

        # Get resolution time if available
        resolution_time = transcript_data.get("resolution_time", 0)
        resolution_str = "N/A"
        if resolution_time is not None and resolution_time > 0:
            hours = int(resolution_time // 60)
            minutes = int(resolution_time % 60)
            resolution_str = f"{hours}h {minutes}m"

        # Get first response time if available
        first_response_time = transcript_data.get("first_response_time", 0)
        response_str = "N/A"
        if first_response_time is not None and first_response_time > 0:
            hours = int(first_response_time // 60)
            minutes = int(first_response_time % 60)
            response_str = f"{hours}h {minutes}m"

        # Get participant count
        participants = transcript_data.get("participants", [])
        participant_count = len(participants)

        # Get closer mention if possible
        closer_mention = f"<@{closed_by_id}>" if closed_by_id else closed_by_name

        # Format timestamps
        created_at = transcript_data.get('created_at', 'Unknown')
        closed_at = transcript_data.get('closed_at', 'Unknown')

        # Try to parse and format dates if they're ISO format
        try:
            if created_at != 'Unknown':
                created_dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                created_at = created_dt.strftime('%Y-%m-%d %H:%M:%S UTC')
        except:
            pass

        try:
            if closed_at != 'Unknown':
                closed_dt = datetime.fromisoformat(closed_at.replace('Z', '+00:00'))
                closed_at = closed_dt.strftime('%Y-%m-%d %H:%M:%S UTC')
        except:
            pass

        # Create a professional embed with modern design
        embed = discord.Embed(
            title=f"📜 Ticket #{ticket_id} Transcript",
            description=f"A support ticket has been closed by staff and archived.",
            color=0x000000,  # Professional black theme
            timestamp=datetime.now()
        )

        # Add ticket information section
        embed.add_field(
            name="Ticket Information",
            value=f"**ID:** {ticket_id}\n**Category:** {category}\n**Created:** {created_at}\n**Closed:** {closed_at}",
            inline=True
        )

        # Add metrics section
        embed.add_field(
            name="Metrics",
            value=f"**Resolution Time:** {resolution_str}\n**First Response:** {response_str}\n**Messages:** {message_count}\n**Participants:** {participant_count}",
            inline=True
        )

        # Add closure information
        embed.add_field(
            name="Closed By",
            value=closer_mention,
            inline=False
        )

        # Add reason if provided (staff-only feature)
        if close_reason and close_reason != "No reason provided":
            embed.add_field(
                name="Closure Reason",
                value=f"📝 {close_reason}",
                inline=False
            )

        # Add footer with transcript ID
        embed.set_footer(text=f"Transcript ID: {transcript_data.get('_id', 'Unknown')}")

        # Generate enhanced transcript files using the new system
        messages = transcript_data.get("messages", [])

        # Only create files if we have messages
        if messages:
            # Send the consolidated transcript embed to transcript channel
            await ticket_rate_limiter.execute(
                'send_transcript',
                transcript_channel.send,
                embed=embed
            )

            # Generate text transcript file
            try:
                text_content = await transcript_formatter.create_enhanced_text_transcript(transcript_data, guild)

                # Create text file
                transcript_file = discord.File(
                    io.StringIO(text_content),
                    filename=f"ticket-transcript-{ticket_id}.txt"
                )

                # Send text file with the main embed
                await ticket_rate_limiter.execute(
                    'send_transcript',
                    transcript_channel.send,
                    file=transcript_file
                )

            except Exception as text_error:
                logger.error(f"Error generating enhanced text transcript: {text_error}")
                # Fall back to basic text transcript
                transcript_lines = []
                for msg in messages:
                    timestamp = msg.get("timestamp", "")
                    author = msg.get("author_name", "Unknown")
                    content = msg.get("content", "")

                    # Try to format timestamp
                    try:
                        dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        timestamp = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        pass

                    transcript_lines.append(f"[{timestamp}] {author}: {content}")

                    # Add attachments if any
                    for attachment in msg.get("attachments", []):
                        transcript_lines.append(f"[Attachment: {attachment}]")

                basic_transcript_content = "\n".join(transcript_lines)

                # Create basic text file
                basic_transcript_file = discord.File(
                    io.StringIO(basic_transcript_content),
                    filename=f"transcript-{ticket_id}.txt"
                )

                # Send basic text file
                await ticket_rate_limiter.execute(
                    'send_transcript',
                    transcript_channel.send,
                    file=basic_transcript_file
                )
        else:
            # Send embed only if no messages
            await ticket_rate_limiter.execute(
                'send_transcript',
                transcript_channel.send,
                embed=embed
            )

        return True

    except Exception as e:
        logger.error(f"Error sending transcript to channel: {e}")
        traceback.print_exc()
        return False

async def send_transcript_saved_notification(channel, ticket_id, transcript_id=None):
    """Send a professional notification that a transcript has been saved"""
    try:
        # Create a professional black-themed embed
        embed = discord.Embed(
            title="📜 Transcript Saved",
            description="The ticket transcript has been successfully saved and archived.",
            color=0x000000,  # Professional black theme
            timestamp=datetime.now()
        )

        # Add ticket information
        embed.add_field(
            name="Ticket Information",
            value=f"**Ticket ID:** {ticket_id}\n**Status:** Archived\n**Saved:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}",
            inline=False
        )

        # Add transcript ID if available
        if transcript_id:
            embed.add_field(
                name="Transcript ID",
                value=f"`{transcript_id}`",
                inline=True
            )

        # Add footer
        embed.set_footer(text="Ticket System • Transcript Archive")

        # Send the notification
        await channel.send(embed=embed)

    except Exception as e:
        logger.error(f"Error sending transcript saved notification: {e}")

class CloseWithReasonModal(discord.ui.Modal):
	"""Modal for staff to enter a closure reason"""
	def __init__(self):
		super().__init__(title="Close Ticket with Reason")

		self.reason_input = discord.ui.TextInput(
			label="Closure Reason",
			placeholder="Enter the reason for closing this ticket...",
			style=discord.TextStyle.paragraph,
			max_length=500,
			required=True
		)
		self.add_item(self.reason_input)

	async def on_submit(self, interaction: discord.Interaction):
		"""Handle the modal submission"""
		try:
			# Check if user has staff role
			has_staff_role = False
			for role_id in ticket_config.get("staff_roles", []):
				role = interaction.guild.get_role(role_id)
				if role and role in interaction.user.roles:
					has_staff_role = True
					break

			if not has_staff_role:
				# Create professional error embed
				error_embed = discord.Embed(
					title="❌ Access Denied",
					description="Only staff members can close tickets with reasons.",
					color=0xff0000  # Red color for error
				)
				error_embed.add_field(
					name="Required Permission",
					value="Staff Role",
					inline=True
				)
				await interaction.response.send_message(
					embed=error_embed,
					ephemeral=True
				)
				return

			# Defer the response
			await interaction.response.defer(ephemeral=True)

			# Close the ticket with the provided reason
			reason = self.reason_input.value
			success, error = await close_ticket(interaction.channel_id, closer=interaction.user, reason=reason)

			if not success:
				await interaction.followup.send(
					f"Error closing ticket: {error}",
					ephemeral=True
				)

		except Exception as e:
			logger.error(f"Error in close with reason modal: {e}")
			traceback.print_exc()
			await interaction.followup.send(
				"An error occurred while closing the ticket.",
				ephemeral=True
			)

# CloseConfirmation class removed - not needed for current implementation
# Normal ticket closure is handled directly without confirmation modal

async def handle_claim_button(interaction: discord.Interaction):
	"""Handle the claim button click - staff only"""
	# Check if user has staff role
	has_staff_role = False
	for role_id in ticket_config.get("staff_roles", []):
		role = interaction.guild.get_role(role_id)
		if role and role in interaction.user.roles:
			has_staff_role = True
			break

	if not has_staff_role:
		await interaction.response.send_message(
			"Only staff members can claim tickets.",
			ephemeral=True
		)
		return

	# Claim the ticket - silently acknowledge the interaction
	await interaction.response.defer(ephemeral=True)
	success, error = await claim_ticket(interaction.channel_id, interaction.user)

	# Only send a message if there was an error
	if not success:
		await interaction.followup.send(
			f"Error claiming ticket: {error}",
			ephemeral=True
		)

# handle_close_button function removed - not needed for current implementation
# Normal ticket closure is handled directly by bot.py without confirmation modal








async def reopen_callback(interaction: discord.Interaction):
	try:
		# Check if user has staff role
		has_staff_role = False
		for role_id in ticket_config["staff_roles"]:
			role = interaction.guild.get_role(role_id)
			if role and role in interaction.user.roles:
				has_staff_role = True
				break

		if not has_staff_role:
			await interaction.response.send_message("You don't have permission to reopen tickets.", ephemeral=True)
			return

		# Defer the response immediately
		await interaction.response.defer(ephemeral=True)

		success, error = await reopen_ticket(interaction.channel.id, interaction.user)

		if success:
			await interaction.followup.send("Ticket reopened successfully!", ephemeral=True)
		else:
			await interaction.followup.send(f"Error reopening ticket: {error}", ephemeral=True)

	except Exception as e:
		print(f"Error in reopen_callback: {e}")
		await interaction.followup.send(f"Error reopening ticket: {str(e)}", ephemeral=True)


async def setup_buttons(channel, close_embed):
	view = View()

	# Add Reopen button
	reopen_btn = Button(label="🔓 Reopen", custom_id="reopen_ticket", style=discord.ButtonStyle.green)
	reopen_btn.callback = reopen_callback
	view.add_item(reopen_btn)

	# Add Transcript button (staff only - sends to channel)
	transcript_btn = Button(
		label="📜 Transcript",
		style=discord.ButtonStyle.blurple,
		custom_id="view_transcript_channel"
	)
	transcript_btn.callback = handle_transcript_button_channel
	view.add_item(transcript_btn)

	# Add Delete Channel button
	delete_channel_btn = discord.ui.Button(
		label="🗑️ Delete",
		style=discord.ButtonStyle.red,
		custom_id="delete_channel"
	)

	async def delete_channel_callback(interaction: discord.Interaction):
		try:
			# Check if user has staff role
			has_staff_role = False
			for role_id in ticket_config["staff_roles"]:
				role = interaction.guild.get_role(role_id)
				if role and role in interaction.user.roles:
					has_staff_role = True
					break

			if not has_staff_role:
				await interaction.response.send_message(
					"You don't have permission to delete tickets.",
					ephemeral=True
				)
				return

			await channel.delete()
		except discord.Forbidden:
			await interaction.response.send_message(
				"Missing permissions to delete channel.",
				ephemeral=True
			)
		except Exception as e:
			await interaction.response.send_message(
				f"Error deleting channel: {str(e)}",
				ephemeral=True
			)

	delete_channel_btn.callback = delete_channel_callback
	view.add_item(delete_channel_btn)

	try:
		await channel.send(embed=close_embed, view=view)
		return True, None
	except Exception as e:
		print(f"Error setting up buttons: {e}")
		return False, str(e)


async def handle_transcript_button_channel(interaction: discord.Interaction):
    """Handle transcript button click - sends notification to ticket channel and transcript to transcript channel (staff only)"""
    try:
        # Check if user has staff role
        has_staff_role = False
        for role_id in ticket_config.get("staff_roles", []):
            role = interaction.guild.get_role(role_id)
            if role and role in interaction.user.roles:
                has_staff_role = True
                break

        if not has_staff_role:
            await interaction.response.send_message(
                "You don't have permission to view transcripts.",
                ephemeral=True
            )
            return

        # Check if transcript channel is configured
        transcript_channel_id = ticket_config.get("transcript_channel")
        if not transcript_channel_id:
            await interaction.response.send_message(
                "No transcript channel is configured. Please contact an administrator.",
                ephemeral=True
            )
            return

        transcript_channel = interaction.guild.get_channel(transcript_channel_id)
        if not transcript_channel:
            await interaction.response.send_message(
                "Transcript channel not found. Please contact an administrator.",
                ephemeral=True
            )
            return

        # Defer response while we process the transcript
        await interaction.response.defer(ephemeral=True)

        # Extract ticket number from channel name
        ticket_number = None
        if interaction.channel.name.startswith('ticket-'):
            ticket_number = interaction.channel.name.split('-')[1]
        elif interaction.channel.name.startswith('closed-'):
            ticket_number = interaction.channel.name.split('-')[1]

        if not ticket_number:
            await interaction.followup.send(
                "Could not determine ticket number from channel name.",
                ephemeral=True
            )
            return

        # Try to get transcript from database first
        transcript_data = None
        try:
            # Import transcript manager
            from transcript_manager import transcript_manager

            # Try to get transcript by ticket ID
            transcript_data = await transcript_manager.get_transcript(ticket_id=ticket_number)

            if transcript_data:
                # Send the stored transcript to transcript channel
                await send_transcript_to_channel(
                    interaction.guild,
                    transcript_data,
                    transcript_channel_id
                )

                # Send simple notification to ticket channel
                notification_embed = discord.Embed(
                    title="📜 Transcript Saved",
                    description="The transcript has been saved to the transcript channel.",
                    color=0x000000,  # Professional black theme
                    timestamp=datetime.now(timezone.utc)
                )

                notification_embed.add_field(
                    name="Ticket ID",
                    value=f"#{ticket_number}",
                    inline=True
                )

                notification_embed.add_field(
                    name="Requested by",
                    value=interaction.user.mention,
                    inline=True
                )

                # Send notification to ticket channel
                await interaction.channel.send(embed=notification_embed)

                return

        except ImportError:
            logger.warning("Transcript manager not available, falling back to channel history")
        except Exception as db_error:
            logger.error(f"Error retrieving transcript from database: {db_error}")

        # If we get here, either there's no stored transcript or we couldn't retrieve it
        # Fall back to gathering messages from channel history

        # Acknowledge the request
        await interaction.followup.send("Generating transcript...", ephemeral=True)

        # Gather messages with enhanced metadata using the improved collection system
        messages = await collect_ticket_messages(interaction.channel)

        # Extract participants and staff participants
        participants = set()
        staff_participants = set()
        staff_role_ids = ticket_config.get("staff_roles", [])

        for msg in messages:
            author_id = msg.get("author_id")
            if author_id:
                participants.add(author_id)

                # Check if author is staff based on roles
                author_roles = msg.get("author_roles", [])
                if any(role_id in staff_role_ids for role_id in author_roles):
                    staff_participants.add(author_id)

        # Determine ticket creation time from first message
        created_at = datetime.now().isoformat()
        if messages:
            first_msg = messages[0]
            created_at = first_msg.get("timestamp", created_at)

        # Get category information
        category_name = "Support"
        category_id = None
        if interaction.channel.category:
            category_id = interaction.channel.category.id
            category_id_str = str(category_id)
            if category_id_str in ticket_config.get("categories", {}):
                category_name = ticket_config["categories"][category_id_str].get("name", "Support")

        # Create enhanced transcript data structure
        transcript_data = {
            "ticket_id": ticket_number,
            "guild_id": interaction.guild.id,
            "channel_id": interaction.channel.id,
            "category": category_name,
            "category_id": category_id,
            "created_at": created_at,
            "closed_at": datetime.now().isoformat(),
            "closed_by": interaction.user.id,
            "closed_by_name": interaction.user.name,
            "close_reason": "Manual transcript request by staff",
            "messages": messages,
            "message_count": len(messages),
            "participants": list(participants),
            "staff_participants": list(staff_participants),
            "staff_roles": staff_role_ids,
            "resolution_time": None,  # Cannot calculate for manual requests
            "first_response_time": None  # Cannot calculate for manual requests
        }

        # Store the transcript first to get the proper ID
        try:
            transcript_id = await store_transcript_data(transcript_data)
            if transcript_id:
                # Add the transcript ID to the data
                transcript_data["_id"] = transcript_id
                logger.info(f"Stored manual transcript {transcript_id} for ticket {ticket_number}")
        except Exception as e:
            logger.error(f"Error storing manual transcript: {e}")
            # Continue without ID if storage fails

        # Send the transcript to transcript channel (now with proper ID if available)
        await send_transcript_to_channel(
            interaction.guild,
            transcript_data,
            transcript_channel_id
        )

        # Send simple notification to ticket channel
        notification_embed = discord.Embed(
            title="📜 Transcript Saved",
            description="The transcript has been saved to the transcript channel.",
            color=0x000000,  # Professional black theme
            timestamp=datetime.now(timezone.utc)
        )

        notification_embed.add_field(
            name="Ticket ID",
            value=f"#{ticket_number}",
            inline=True
        )

        notification_embed.add_field(
            name="Requested by",
            value=interaction.user.mention,
            inline=True
        )

        # Send notification to ticket channel
        await interaction.channel.send(embed=notification_embed)

    except Exception as e:
        logger.error(f"Error processing transcript: {e}")
        traceback.print_exc()
        await interaction.followup.send(
            "An error occurred while processing the transcript.",
            ephemeral=True
        )

async def add_category(guild, name: str, description: str):
	"""Add a new ticket category"""
	try:
		# Create category in Discord
		category = await guild.create_category(name)

		# Add to ticket config
		ticket_config["categories"][str(category.id)] = {
			"name": name,
			"description": description
		}

		# Save changes
		await save_ticket_data()

		print(f"Added category {name} with ID {category.id}")
		print(f"Updated categories: {ticket_config['categories']}")

		# Update ticket panel
		if ticket_config.get("ticket_channel"):
			channel = bot.get_channel(ticket_config["ticket_channel"])
			if channel:
				await create_ticket_panel(channel)

		return True, category

	except Exception as e:
		print(f"Error adding category: {e}")
		traceback.print_exc()
		return False, str(e)


async def remove_category(category_id: int):
	"""Remove a ticket category"""
	if category_id not in ticket_config["categories"]:
		return False, "Category not found"

	try:
		del ticket_config["categories"][category_id]
		await save_ticket_data()
		return True, None
	except Exception as e:
		return False, str(e)

async def set_staff_role(role_id: int):
	"""Add a staff role to the ticket system"""
	if "staff_roles" not in ticket_config:
		ticket_config["staff_roles"] = []

	if role_id in ticket_config["staff_roles"]:
		return False

	ticket_config["staff_roles"].append(role_id)
	await save_ticket_data()
	return True

async def set_transcript_channel(channel_id: int):
	"""Set the transcript channel for closed tickets"""
	ticket_config["transcript_channel"] = channel_id
	await save_ticket_data()
	return True

async def get_channel(channel_id: int):
	"""Helper function to get channel from ID"""
	for guild in bot.guilds:
		channel = guild.get_channel(channel_id)
		if channel:
			return channel
	return None

async def set_ticket_channel(channel_id: int):
	"""Set the channel where users can create tickets"""
	try:
		ticket_config["ticket_channel"] = channel_id
		await save_ticket_data()

		# Get channel and create panel
		channel = bot.get_channel(channel_id)
		if channel:
			await create_ticket_panel(channel)

		return True
	except Exception as e:
		print(f"Error setting ticket channel: {e}")
		return False
