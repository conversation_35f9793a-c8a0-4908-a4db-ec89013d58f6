"""
VLife RP Ticket Modal Forms
Modal forms for each ticket category with appropriate fields and validation
"""

import discord
import logging
from typing import Optional, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class BaseVLifeTicketModal(discord.ui.Modal):
    """Base modal class for VLife RP tickets"""
    
    def __init__(self, title: str, category_id: str):
        super().__init__(title=title, timeout=300)  # 5 minute timeout
        self.category_id = category_id
    
    async def on_submit(self, interaction: discord.Interaction):
        """Handle modal submission - create ticket"""
        try:
            # Defer response to prevent timeout
            await interaction.response.defer(ephemeral=True)
            
            # Collect form data
            form_data = self.collect_form_data()
            
            # Create ticket
            success, ticket_info = await self.create_ticket(interaction, form_data)
            
            if success:
                embed = discord.Embed(
                    title="🎫 Ticket Created Successfully",
                    description=(
                        f"Your ticket has been created and our **Crimson Team** has been notified.\n\n"
                        f"**Ticket ID:** {ticket_info.get('ticket_id', 'N/A')}\n"
                        f"**Category:** {ticket_info.get('category', 'N/A')}\n"
                        f"**Channel:** {ticket_info.get('channel_mention', 'N/A')}\n\n"
                        f"Please check the ticket channel for further assistance."
                    ),
                    color=0x00FF00  # Green
                )
                embed.set_footer(text="VLife RP Support System")
                
                await interaction.followup.send(embed=embed, ephemeral=True)
            else:
                error_embed = discord.Embed(
                    title="❌ Ticket Creation Failed",
                    description=(
                        "We couldn't create your ticket at this time. This could be due to:\n"
                        "• You already have the maximum number of open tickets\n"
                        "• Server configuration issues\n"
                        "• Temporary system problems\n\n"
                        "Please try again later or contact an administrator."
                    ),
                    color=0xFF0000  # Red
                )
                await interaction.followup.send(embed=error_embed, ephemeral=True)
                
        except Exception as e:
            logger.error(f"Error in modal submission: {e}")
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "❌ An error occurred while creating your ticket. Please try again later.",
                    ephemeral=True
                )
    
    def collect_form_data(self) -> Dict[str, Any]:
        """Collect data from form fields - to be implemented by subclasses"""
        return {}
    
    async def create_ticket(self, interaction: discord.Interaction, form_data: Dict[str, Any]) -> tuple[bool, Dict[str, Any]]:
        """Create ticket with the collected data"""
        try:
            # Import here to avoid circular imports
            from tickets import create_ticket
            
            # Find the appropriate category ID from the existing ticket system
            from tickets import ticket_config
            
            # Map our category IDs to existing ticket categories
            category_mapping = {
                'report_player': 'Player Report',
                'refund_request': 'Refund Request',
                'bug_report': 'Bug Report',
                'whitelist_application': 'Whitelist Application',
                'general_support': 'General Support'
            }
            
            category_name = category_mapping.get(self.category_id, 'General Support')
            
            # Find matching category ID
            category_id = None
            for cat_id, cat_info in ticket_config.get("categories", {}).items():
                if cat_info.get("name", "").lower() == category_name.lower():
                    category_id = int(cat_id)
                    break
            
            if not category_id:
                logger.error(f"Could not find category ID for {category_name}")
                return False, {}
            
            # Create the ticket
            ticket_channel = await create_ticket(interaction, category_id, form_data)
            
            if ticket_channel:
                return True, {
                    'ticket_id': f"#{ticket_config.get('last_ticket_number', 0)}",
                    'category': category_name,
                    'channel_mention': ticket_channel.mention
                }
            else:
                return False, {}
                
        except Exception as e:
            logger.error(f"Error creating ticket: {e}")
            return False, {}


class ReportPlayerModal(BaseVLifeTicketModal):
    """Modal for reporting players"""
    
    def __init__(self):
        super().__init__(title="🚨 Report a Player", category_id="report_player")
        
        # Player name field
        self.player_name = discord.ui.TextInput(
            label="Player Name/ID",
            placeholder="Enter the player's name or ID",
            required=True,
            max_length=100
        )
        self.add_item(self.player_name)
        
        # Rule violation field
        self.violation_type = discord.ui.TextInput(
            label="Rule Violation",
            placeholder="What rule did they break? (RDM, VDM, FailRP, etc.)",
            required=True,
            max_length=200
        )
        self.add_item(self.violation_type)
        
        # Description field
        self.description = discord.ui.TextInput(
            label="Detailed Description",
            placeholder="Describe what happened in detail...",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=1000
        )
        self.add_item(self.description)
        
        # Evidence field
        self.evidence = discord.ui.TextInput(
            label="Evidence (Optional)",
            placeholder="Links to screenshots, videos, or other evidence",
            required=False,
            max_length=500
        )
        self.add_item(self.evidence)
    
    def collect_form_data(self) -> Dict[str, Any]:
        return {
            'type': 'player_report',
            'player_name': self.player_name.value,
            'violation_type': self.violation_type.value,
            'description': self.description.value,
            'evidence': self.evidence.value or 'None provided'
        }


class RefundRequestModal(BaseVLifeTicketModal):
    """Modal for refund requests"""
    
    def __init__(self):
        super().__init__(title="💰 Refund Request", category_id="refund_request")
        
        # Item/money lost field
        self.lost_items = discord.ui.TextInput(
            label="Items/Money Lost",
            placeholder="What did you lose? (e.g., $50,000, Lamborghini, etc.)",
            required=True,
            max_length=200
        )
        self.add_item(self.lost_items)
        
        # Reason field
        self.reason = discord.ui.TextInput(
            label="Reason for Loss",
            placeholder="How did you lose these items? (server crash, bug, etc.)",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=500
        )
        self.add_item(self.reason)
        
        # Time field
        self.time_lost = discord.ui.TextInput(
            label="When Did This Happen?",
            placeholder="Date and approximate time",
            required=True,
            max_length=100
        )
        self.add_item(self.time_lost)
        
        # Evidence field
        self.evidence = discord.ui.TextInput(
            label="Evidence (Optional)",
            placeholder="Screenshots, videos, or other proof",
            required=False,
            max_length=500
        )
        self.add_item(self.evidence)
    
    def collect_form_data(self) -> Dict[str, Any]:
        return {
            'type': 'refund_request',
            'lost_items': self.lost_items.value,
            'reason': self.reason.value,
            'time_lost': self.time_lost.value,
            'evidence': self.evidence.value or 'None provided'
        }


class BugReportModal(BaseVLifeTicketModal):
    """Modal for bug reports"""
    
    def __init__(self):
        super().__init__(title="🐛 Bug Report", category_id="bug_report")
        
        # Bug title field
        self.bug_title = discord.ui.TextInput(
            label="Bug Title",
            placeholder="Brief description of the bug",
            required=True,
            max_length=100
        )
        self.add_item(self.bug_title)
        
        # Steps to reproduce
        self.steps = discord.ui.TextInput(
            label="Steps to Reproduce",
            placeholder="How can we reproduce this bug?",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=500
        )
        self.add_item(self.steps)
        
        # Expected vs actual behavior
        self.behavior = discord.ui.TextInput(
            label="Expected vs Actual Behavior",
            placeholder="What should happen vs what actually happens?",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=500
        )
        self.add_item(self.behavior)
        
        # Additional info
        self.additional_info = discord.ui.TextInput(
            label="Additional Information (Optional)",
            placeholder="Screenshots, error messages, etc.",
            required=False,
            max_length=500
        )
        self.add_item(self.additional_info)
    
    def collect_form_data(self) -> Dict[str, Any]:
        return {
            'type': 'bug_report',
            'bug_title': self.bug_title.value,
            'steps': self.steps.value,
            'behavior': self.behavior.value,
            'additional_info': self.additional_info.value or 'None provided'
        }


class WhitelistApplicationModal(BaseVLifeTicketModal):
    """Modal for whitelist applications"""
    
    def __init__(self):
        super().__init__(title="📋 Whitelist Application", category_id="whitelist_application")
        
        # Character name field
        self.character_name = discord.ui.TextInput(
            label="Character Name",
            placeholder="Your character's full name",
            required=True,
            max_length=100
        )
        self.add_item(self.character_name)
        
        # Character backstory
        self.backstory = discord.ui.TextInput(
            label="Character Backstory",
            placeholder="Tell us about your character's background...",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=1000
        )
        self.add_item(self.backstory)
        
        # RP experience
        self.experience = discord.ui.TextInput(
            label="RP Experience",
            placeholder="Describe your roleplay experience",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=500
        )
        self.add_item(self.experience)
        
        # Why join
        self.why_join = discord.ui.TextInput(
            label="Why VLife RP?",
            placeholder="Why do you want to join our server?",
            required=True,
            max_length=300
        )
        self.add_item(self.why_join)
    
    def collect_form_data(self) -> Dict[str, Any]:
        return {
            'type': 'whitelist_application',
            'character_name': self.character_name.value,
            'backstory': self.backstory.value,
            'experience': self.experience.value,
            'why_join': self.why_join.value
        }


class GeneralSupportModal(BaseVLifeTicketModal):
    """Modal for general support"""
    
    def __init__(self):
        super().__init__(title="❓ General Support", category_id="general_support")
        
        # Subject field
        self.subject = discord.ui.TextInput(
            label="Subject",
            placeholder="What do you need help with?",
            required=True,
            max_length=100
        )
        self.add_item(self.subject)
        
        # Description field
        self.description = discord.ui.TextInput(
            label="Description",
            placeholder="Please describe your question or issue in detail...",
            style=discord.TextStyle.paragraph,
            required=True,
            max_length=1000
        )
        self.add_item(self.description)
        
        # Priority field
        self.priority = discord.ui.TextInput(
            label="Priority (Optional)",
            placeholder="Low, Medium, High, or Urgent",
            required=False,
            max_length=20
        )
        self.add_item(self.priority)
    
    def collect_form_data(self) -> Dict[str, Any]:
        return {
            'type': 'general_support',
            'subject': self.subject.value,
            'description': self.description.value,
            'priority': self.priority.value or 'Medium'
        }


def get_modal_for_category(category_id: str) -> Optional[BaseVLifeTicketModal]:
    """Get the appropriate modal for a ticket category"""
    modals = {
        'report_player': ReportPlayerModal,
        'refund_request': RefundRequestModal,
        'bug_report': BugReportModal,
        'whitelist_application': WhitelistApplicationModal,
        'general_support': GeneralSupportModal
    }
    
    modal_class = modals.get(category_id)
    if modal_class:
        return modal_class()
    
    logger.error(f"Unknown ticket category: {category_id}")
    return None
