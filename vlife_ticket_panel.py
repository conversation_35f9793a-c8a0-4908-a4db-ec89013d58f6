"""
VLife RP Discord Ticket Panel System
Modern GTA RP-themed ticket panel with fixed dimensions and button interactions
"""

import discord
import logging
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any, List
from image_utils import ImageProcessor, image_cache
from database import DatabaseManager

logger = logging.getLogger(__name__)

class VLifeRPConfig:
    """Configuration constants for VLife RP ticket panel"""
    
    # Color scheme
    COLORS = {
        'primary': 0xDC143C,    # Crimson red
        'secondary': 0x2C2C2C,  # Dark gray
        'accent': 0xFFD700,     # Gold
        'success': 0x00FF00,    # Green
        'danger': 0xFF0000,     # Red
        'warning': 0xFFA500     # Orange
    }
    
    # Fixed embed dimensions
    EMBED_WIDTH = 400  # Discord's standard embed width
    
    # Default content
    DEFAULT_TITLE = "🌃 VLife RP Support Center"
    DEFAULT_DESCRIPTION = ("⚠️ **Got a problem on the streets of Los Santos?** Whether it's a stolen ride, "
                          "broken system, or corrupt cop, our **Crimson Team** is on patrol to back you up.")
    
    # Support information
    SUPPORT_HOURS = "🕒 **Support Hours**\nMon–Fri: 9:00 AM – 5:00 PM PST\nSat–Sun: Limited Availability"
    RESPONSE_TIME = "⏱️ **Response Time**\nTypically within 24 hours\nPriority tickets: 2-4 hours"
    
    # Button categories with emojis and styles
    TICKET_CATEGORIES = {
        'report_player': {
            'label': 'Report Player',
            'emoji': '🚨',
            'style': discord.ButtonStyle.danger,
            'description': 'Report rule violations or problematic behavior'
        },
        'refund_request': {
            'label': 'Refund Request',
            'emoji': '💰',
            'style': discord.ButtonStyle.success,
            'description': 'Request refunds for lost items or money'
        },
        'bug_report': {
            'label': 'Bug Report',
            'emoji': '🐛',
            'style': discord.ButtonStyle.secondary,
            'description': 'Report technical issues or bugs'
        },
        'whitelist_application': {
            'label': 'Whitelist Application',
            'emoji': '📋',
            'style': discord.ButtonStyle.primary,
            'description': 'Apply for server whitelist access'
        },
        'general_support': {
            'label': 'General Support',
            'emoji': '❓',
            'style': discord.ButtonStyle.secondary,
            'description': 'General questions and assistance'
        }
    }


class VLifeRPTicketEmbed:
    """Handles creation of VLife RP themed ticket embeds with fixed dimensions"""
    
    def __init__(self, guild: discord.Guild):
        self.guild = guild
        self.db = DatabaseManager()
    
    async def create_ticket_panel_embed(self, 
                                      custom_image_url: Optional[str] = None,
                                      custom_thumbnail_url: Optional[str] = None) -> discord.Embed:
        """
        Create the main ticket panel embed with fixed dimensions and consistent layout
        
        Args:
            custom_image_url: Optional custom banner image URL
            custom_thumbnail_url: Optional custom thumbnail URL
            
        Returns:
            Discord embed with fixed layout and dimensions
        """
        try:
            # Create base embed with fixed color and title
            embed = discord.Embed(
                title=VLifeRPConfig.DEFAULT_TITLE,
                description=VLifeRPConfig.DEFAULT_DESCRIPTION,
                color=VLifeRPConfig.COLORS['primary']
            )
            
            # Add banner image with fixed dimensions
            if custom_image_url:
                # Process image to fixed banner dimensions
                processed_image = await ImageProcessor.process_image_for_embed(
                    custom_image_url, "banner"
                )
                if processed_image:
                    embed.set_image(url=f"attachment://{processed_image.filename}")
                else:
                    # Fallback to default banner
                    embed.set_image(url="attachment://vlife_default_banner.png")
            else:
                # Use default VLife RP banner
                embed.set_image(url="attachment://vlife_default_banner.png")
            
            # Add thumbnail with fixed dimensions
            if custom_thumbnail_url:
                processed_thumbnail = await ImageProcessor.process_image_for_embed(
                    custom_thumbnail_url, "thumbnail"
                )
                if processed_thumbnail:
                    embed.set_thumbnail(url=f"attachment://{processed_thumbnail.filename}")
                else:
                    embed.set_thumbnail(url="attachment://vlife_default_thumbnail.png")
            elif self.guild.icon:
                # Use guild icon as thumbnail
                embed.set_thumbnail(url=self.guild.icon.url)
            else:
                # Use default VLife RP thumbnail
                embed.set_thumbnail(url="attachment://vlife_default_thumbnail.png")
            
            # Add support information fields with consistent formatting
            embed.add_field(
                name=VLifeRPConfig.SUPPORT_HOURS.split('\n')[0],  # Just the title
                value='\n'.join(VLifeRPConfig.SUPPORT_HOURS.split('\n')[1:]),  # The content
                inline=True
            )
            
            embed.add_field(
                name=VLifeRPConfig.RESPONSE_TIME.split('\n')[0],  # Just the title
                value='\n'.join(VLifeRPConfig.RESPONSE_TIME.split('\n')[1:]),  # The content
                inline=True
            )
            
            # Add empty field for layout consistency
            embed.add_field(name="\u200b", value="\u200b", inline=True)
            
            # Add live statistics if available
            stats = await self.get_live_statistics()
            if stats:
                embed.add_field(
                    name="📊 **Live Statistics**",
                    value=(
                        f"✅ **Tickets Closed This Week:** {stats.get('closed_this_week', 0)}\n"
                        f"📁 **Active Tickets:** {stats.get('active_tickets', 0)}\n"
                        f"⏳ **Avg. Resolution Time:** {stats.get('avg_resolution', 'N/A')}"
                    ),
                    inline=False
                )
            
            # Add footer with timestamp and branding
            current_time = datetime.now().strftime("%m/%d/%Y %I:%M %p")
            embed.set_footer(
                text=f"VLife RP • Powered by Crimson Team • {current_time}",
                icon_url=self.guild.icon.url if self.guild.icon else None
            )
            
            return embed
            
        except Exception as e:
            logger.error(f"Error creating ticket panel embed: {e}")
            # Return a basic fallback embed
            return discord.Embed(
                title="🌃 VLife RP Support Center",
                description="Support panel temporarily unavailable. Please try again later.",
                color=VLifeRPConfig.COLORS['danger']
            )
    
    async def get_live_statistics(self) -> Optional[Dict[str, Any]]:
        """Get live ticket statistics from database"""
        try:
            if not await self.db.connect():
                return None
            
            # Get tickets collection
            tickets_collection = self.db.get_collection("tickets")
            if not tickets_collection:
                return None
            
            # Calculate statistics
            from datetime import datetime, timedelta
            
            # Get current week start (Monday)
            now = datetime.now()
            week_start = now - timedelta(days=now.weekday())
            week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)
            
            # Count closed tickets this week
            closed_this_week = await tickets_collection.count_documents({
                "guild_id": self.guild.id,
                "status": "closed",
                "closed_at": {"$gte": week_start}
            })
            
            # Count active tickets
            active_tickets = await tickets_collection.count_documents({
                "guild_id": self.guild.id,
                "status": {"$in": ["open", "in_progress"]}
            })
            
            # Calculate average resolution time (last 30 days)
            thirty_days_ago = now - timedelta(days=30)
            
            pipeline = [
                {
                    "$match": {
                        "guild_id": self.guild.id,
                        "status": "closed",
                        "closed_at": {"$gte": thirty_days_ago},
                        "created_at": {"$exists": True}
                    }
                },
                {
                    "$project": {
                        "resolution_time": {
                            "$subtract": ["$closed_at", "$created_at"]
                        }
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "avg_resolution_ms": {"$avg": "$resolution_time"}
                    }
                }
            ]
            
            avg_result = await tickets_collection.aggregate(pipeline).to_list(1)
            
            if avg_result and avg_result[0].get("avg_resolution_ms"):
                # Convert milliseconds to hours
                avg_hours = avg_result[0]["avg_resolution_ms"] / (1000 * 60 * 60)
                if avg_hours < 1:
                    avg_resolution = f"{int(avg_hours * 60)}m"
                elif avg_hours < 24:
                    avg_resolution = f"{int(avg_hours)}h"
                else:
                    avg_resolution = f"{int(avg_hours / 24)}d"
            else:
                avg_resolution = "N/A"
            
            return {
                "closed_this_week": closed_this_week,
                "active_tickets": active_tickets,
                "avg_resolution": avg_resolution
            }
            
        except Exception as e:
            logger.error(f"Error getting live statistics: {e}")
            return None
    
    async def get_embed_files(self, 
                            custom_image_url: Optional[str] = None,
                            custom_thumbnail_url: Optional[str] = None) -> List[discord.File]:
        """
        Get all files needed for the embed (processed images)
        
        Returns:
            List of discord.File objects for the embed
        """
        files = []
        
        try:
            # Process banner image
            if custom_image_url:
                banner_file = await ImageProcessor.process_image_for_embed(
                    custom_image_url, "banner"
                )
                if banner_file:
                    files.append(banner_file)
                else:
                    # Add default banner
                    default_banner_data = ImageProcessor.create_default_banner("VLife RP")
                    files.append(discord.File(
                        io.BytesIO(default_banner_data), 
                        filename="vlife_default_banner.png"
                    ))
            else:
                # Add default banner
                import io
                default_banner_data = ImageProcessor.create_default_banner("VLife RP")
                files.append(discord.File(
                    io.BytesIO(default_banner_data), 
                    filename="vlife_default_banner.png"
                ))
            
            # Process thumbnail image
            if custom_thumbnail_url:
                thumbnail_file = await ImageProcessor.process_image_for_embed(
                    custom_thumbnail_url, "thumbnail"
                )
                if thumbnail_file:
                    files.append(thumbnail_file)
                else:
                    # Add default thumbnail if guild doesn't have icon
                    if not self.guild.icon:
                        default_thumbnail_data = ImageProcessor.create_default_thumbnail("RP")
                        files.append(discord.File(
                            io.BytesIO(default_thumbnail_data), 
                            filename="vlife_default_thumbnail.png"
                        ))
            elif not self.guild.icon:
                # Add default thumbnail if guild doesn't have icon
                import io
                default_thumbnail_data = ImageProcessor.create_default_thumbnail("RP")
                files.append(discord.File(
                    io.BytesIO(default_thumbnail_data), 
                    filename="vlife_default_thumbnail.png"
                ))
            
            return files
            
        except Exception as e:
            logger.error(f"Error getting embed files: {e}")
            return []


class VLifeRPTicketButtons(discord.ui.View):
    """Button view for VLife RP ticket categories"""
    
    def __init__(self):
        super().__init__(timeout=None)  # Persistent view
        
        # Add buttons for each ticket category
        for category_id, category_info in VLifeRPConfig.TICKET_CATEGORIES.items():
            button = VLifeRPTicketButton(
                category_id=category_id,
                label=category_info['label'],
                emoji=category_info['emoji'],
                style=category_info['style']
            )
            self.add_item(button)


class VLifeRPTicketButton(discord.ui.Button):
    """Individual ticket category button"""
    
    def __init__(self, category_id: str, label: str, emoji: str, style: discord.ButtonStyle):
        super().__init__(
            label=label,
            emoji=emoji,
            style=style,
            custom_id=f"vlife_ticket_{category_id}"
        )
        self.category_id = category_id
    
    async def callback(self, interaction: discord.Interaction):
        """Handle button click - will be implemented in the modal system"""
        try:
            # Import here to avoid circular imports
            from vlife_ticket_modals import get_modal_for_category
            
            # Get the appropriate modal for this category
            modal = get_modal_for_category(self.category_id)
            
            if modal:
                await interaction.response.send_modal(modal)
            else:
                await interaction.response.send_message(
                    "❌ This ticket category is temporarily unavailable. Please try again later.",
                    ephemeral=True
                )
                
        except Exception as e:
            logger.error(f"Error handling ticket button click: {e}")
            if not interaction.response.is_done():
                await interaction.response.send_message(
                    "❌ An error occurred while processing your request. Please try again later.",
                    ephemeral=True
                )


async def create_vlife_ticket_panel(channel: discord.TextChannel,
                                  custom_image_url: Optional[str] = None,
                                  custom_thumbnail_url: Optional[str] = None) -> bool:
    """
    Create and send the VLife RP ticket panel to the specified channel
    
    Args:
        channel: Discord channel to send the panel to
        custom_image_url: Optional custom banner image URL
        custom_thumbnail_url: Optional custom thumbnail URL
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create embed handler
        embed_handler = VLifeRPTicketEmbed(channel.guild)
        
        # Create embed and get files
        embed = await embed_handler.create_ticket_panel_embed(
            custom_image_url, custom_thumbnail_url
        )
        files = await embed_handler.get_embed_files(
            custom_image_url, custom_thumbnail_url
        )
        
        # Create button view
        view = VLifeRPTicketButtons()
        
        # Clear existing bot messages in the channel
        try:
            async for message in channel.history(limit=10):
                if message.author == channel.guild.me:
                    await message.delete()
        except Exception as e:
            logger.warning(f"Could not clear old messages: {e}")
        
        # Send the panel
        message = await channel.send(embed=embed, files=files, view=view)
        
        logger.info(f"VLife RP ticket panel created successfully in {channel.name} (ID: {message.id})")
        return True
        
    except Exception as e:
        logger.error(f"Error creating VLife RP ticket panel: {e}")
        return False
